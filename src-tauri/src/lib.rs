// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
use btleplug::api::{Central, CentralEvent, Manager as _, Peripheral as _, ScanFilter};
use btleplug::platform::{<PERSON><PERSON><PERSON>, Manager, Peripheral};
use futures_util::StreamExt;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager as _, State};
use tokio::sync::broadcast;

#[derive(Debug, serde::Deserialize)]
struct StartScanArgs {
    #[serde(alias = "manufacturerId", alias = "manufacturer_id")]
    manufacturer_id: Option<u16>,
}

fn build_device_from_properties(
    id: &btleplug::platform::PeripheralId,
    properties: &btleplug::api::PeripheralProperties,
) -> BleDevice {
    let address = id.to_string();
    let rssi = properties.rssi.unwrap_or(0);
    let manufacturer_data = properties
        .manufacturer_data
        .iter()
        .map(|(k, v)| (k.to_string(), v.clone()))
        .collect();
    let name = properties
        .local_name
        .clone()
        .unwrap_or_else(|| address.clone());

    let services: Vec<String> = properties.services.iter().map(|u| u.to_string()).collect();

    BleDevice {
        name,
        address,
        rssi,
        is_connected: false,
        manufacturer_data,
        services,
    }
}

// 定义设备信息结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct BleDevice {
    pub name: String,
    pub address: String,
    pub rssi: i16,
    pub is_connected: bool,
    pub manufacturer_data: HashMap<String, Vec<u8>>,
    pub services: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize)]
struct DeviceServicesUpdate {
    address: String,
    services: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize)]
struct ServiceCacheInfo {
    address: String,
    advertised_services: Vec<String>,
    discovered_services: Vec<String>,
    merged_services: Vec<String>,
    last_updated: u64, // Unix timestamp
}

/// 设备服务缓存，用于合并广播服务和连接后发现的服务
#[derive(Debug, Clone)]
struct DeviceServiceCache {
    /// 广播中宣告的服务（扫描时获取）
    advertised_services: Vec<String>,
    /// 连接后发现的服务（GATT 服务发现）
    discovered_services: Vec<String>,
    /// 最后更新时间戳
    last_updated: std::time::SystemTime,
}

impl DeviceServiceCache {
    fn new() -> Self {
        Self {
            advertised_services: Vec::new(),
            discovered_services: Vec::new(),
            last_updated: std::time::SystemTime::now(),
        }
    }

    /// 更新广播服务
    fn update_advertised_services(&mut self, services: Vec<String>) {
        self.advertised_services = services;
        self.last_updated = std::time::SystemTime::now();
    }

    /// 更新发现的服务
    fn update_discovered_services(&mut self, services: Vec<String>) {
        self.discovered_services = services;
        self.last_updated = std::time::SystemTime::now();
    }

    /// 获取合并后的服务列表（去重并排序）
    fn get_merged_services(&self) -> Vec<String> {
        let mut merged = Vec::new();
        merged.extend(self.advertised_services.iter().cloned());
        merged.extend(self.discovered_services.iter().cloned());

        // 去重并排序
        merged.sort();
        merged.dedup();
        merged
    }

    /// 获取最佳可用的服务列表
    /// 优先返回发现的服务，如果为空则返回广播服务
    fn get_best_services(&self) -> Vec<String> {
        if !self.discovered_services.is_empty() {
            self.get_merged_services()
        } else if !self.advertised_services.is_empty() {
            self.advertised_services.clone()
        } else {
            Vec::new()
        }
    }
}

// 定义应用状态
struct AppState {
    central: Option<Adapter>,
    peripherals: HashMap<String, Peripheral>,
    scanning_tx: Option<broadcast::Sender<Vec<BleDevice>>>,
    connection_tx: Option<broadcast::Sender<bool>>,
    /// 设备服务缓存，key 为设备地址
    device_services: HashMap<String, DeviceServiceCache>,
}

impl AppState {
    fn new() -> Self {
        Self {
            central: None,
            peripherals: HashMap::new(),
            scanning_tx: None,
            connection_tx: None,
            device_services: HashMap::new(),
        }
    }

    /// 更新设备的广播服务
    fn update_advertised_services(&mut self, address: &str, services: Vec<String>) {
        let cache = self.device_services.entry(address.to_string()).or_insert_with(DeviceServiceCache::new);
        cache.update_advertised_services(services);
    }

    /// 更新设备的发现服务
    fn update_discovered_services(&mut self, address: &str, services: Vec<String>) {
        let cache = self.device_services.entry(address.to_string()).or_insert_with(DeviceServiceCache::new);
        cache.update_discovered_services(services);
    }

    /// 获取设备的合并服务
    fn get_device_services(&self, address: &str) -> Vec<String> {
        self.device_services
            .get(address)
            .map(|cache| cache.get_best_services())
            .unwrap_or_default()
    }

    /// 获取设备的广播服务（用于向后兼容）
    fn get_advertised_services(&self, address: &str) -> Vec<String> {
        self.device_services
            .get(address)
            .map(|cache| cache.advertised_services.clone())
            .unwrap_or_default()
    }
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 获取蓝牙适配器
#[tauri::command]
async fn get_adapter() -> Result<(), String> {
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;
    if adapters.is_empty() {
        return Err("No Bluetooth adapters found".to_string());
    }
    Ok(())
}

// 开始扫描设备
#[tauri::command]
async fn start_scan(
    app_handle: AppHandle,
    state: State<'_, Arc<Mutex<AppState>>>,
    args: Option<StartScanArgs>,
) -> Result<(), String> {
    let manufacturer_id = args.and_then(|a| a.manufacturer_id);
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;
    let central = adapters
        .into_iter()
        .next()
        .ok_or("No Bluetooth adapters found".to_string())?;

    let (tx, _) = broadcast::channel(16);
    let tx_clone = tx.clone();

    {
        let mut state = state.lock().unwrap();
        state.central = Some(central.clone());
        state.scanning_tx = Some(tx);
        state.peripherals.clear();
    }

    // 监听蓝牙事件
    let mut events = central.events().await.map_err(|e| e.to_string())?;
    let central_clone = central.clone();
    let app_handle_clone = app_handle.clone();
    let vendor_filter = manufacturer_id;

    // 启动扫描任务
    tauri::async_runtime::spawn(async move {
        // 清除之前的扫描结果
        let mut device_map: HashMap<String, BleDevice> = HashMap::new();

        // 统一处理属性 → 设备、过滤、发送与广告服务缓存的辅助函数
        fn handle_properties(
            id: &btleplug::platform::PeripheralId,
            properties: &btleplug::api::PeripheralProperties,
            vendor_filter: Option<u16>,
            app_handle: &AppHandle,
            device_map: &mut HashMap<String, BleDevice>,
            tx: &broadcast::Sender<Vec<BleDevice>>,
        ) {
            // 按可选的厂商 ID 过滤
            if let Some(filter_id) = vendor_filter {
                if properties.manufacturer_data.is_empty() {
                    return;
                }
                if !properties.manufacturer_data.contains_key(&filter_id) {
                    return;
                }
            }

            let address = id.to_string();
            let mut device = build_device_from_properties(id, properties);

            // 获取当前广播的服务
            let current_services = device.services.clone();

            // 更新设备的广播服务缓存
            if !current_services.is_empty() {
                let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
                let mut state = state_guard.lock().unwrap();
                state.update_advertised_services(&address, current_services.clone());
            }

            // 获取合并后的服务（包括之前缓存的服务）
            let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
            let state = state_guard.lock().unwrap();
            let merged_services = state.get_device_services(&address);
            drop(state);

            // 如果当前服务为空，使用缓存的服务
            if device.services.is_empty() && !merged_services.is_empty() {
                device.services = merged_services;
            } else if !device.services.is_empty() && !merged_services.is_empty() {
                // 如果都有服务，使用合并后的服务
                device.services = merged_services;
            }

            // 如果仍然没有服务，尝试从之前的设备映射中获取
            if device.services.is_empty() {
                if let Some(prev) = device_map.get(&address) {
                    if !prev.services.is_empty() {
                        device.services = prev.services.clone();
                    }
                }
            }

            device_map.insert(address.clone(), device.clone());
            let devices: Vec<BleDevice> = device_map.values().cloned().collect();
            let _ = tx.send(devices);
        }

        // 开始扫描
        if let Err(err) = central_clone.start_scan(ScanFilter::default()).await {
            eprintln!("Error starting scan: {:?}", err);
            return;
        }

        // 处理扫描到的设备
        while let Some(event) = events.next().await {
            match event {
                CentralEvent::DeviceDiscovered(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            // 先保存外设引用，便于后续 DeviceUpdated 使用
                            let address = id.to_string();
                            let state_guard = app_handle_clone.state::<Arc<Mutex<AppState>>>();
                            let mut state = state_guard.lock().unwrap();
                            state.peripherals.insert(address, peripheral);
                            drop(state);

                            handle_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                CentralEvent::DeviceUpdated(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            handle_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                _ => {}
            }
        }
    });

    Ok(())
}

// 停止扫描
#[tauri::command]
async fn stop_scan(state: State<'_, Arc<Mutex<AppState>>>) -> Result<(), String> {
    let central = {
        let state = state.lock().unwrap();
        state.central.clone()
    };

    if let Some(central) = central {
        central.stop_scan().await.map_err(|e| e.to_string())?;
    }
    Ok(())
}

// 连接设备
#[tauri::command]
async fn connect_device(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let (peripheral, connection_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        (peripheral, connection_tx)
    };

    if let Some(peripheral) = peripheral {
        // 连接设备
        if let Err(err) = peripheral.connect().await {
            return Err(err.to_string());
        }

        // 发现服务
        if let Err(err) = peripheral.discover_services().await {
            return Err(err.to_string());
        }

        // 获取连接后发现的服务
        let discovered_services: Vec<String> = peripheral
            .services()
            .iter()
            .map(|s| s.uuid.to_string())
            .collect();

        // 更新设备的发现服务缓存
        {
            let mut state_locked = state.lock().unwrap();
            state_locked.update_discovered_services(&address, discovered_services);
        }

        // 发送连接状态更新
        if let Some(tx) = &connection_tx {
            let _ = tx.send(true);
        }

        return Ok(());
    }
    Err("Device not found".to_string())
}

// 断开连接
#[tauri::command]
async fn disconnect_device(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let (peripheral, connection_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        (peripheral, connection_tx)
    };

    if let Some(peripheral) = peripheral {
        if let Err(err) = peripheral.disconnect().await {
            return Err(err.to_string());
        }

        // 发送连接状态更新
        if let Some(tx) = &connection_tx {
            let _ = tx.send(false);
        }

        return Ok(());
    }
    Err("Device not found".to_string())
}

// 获取设备服务
#[tauri::command]
async fn get_device_services(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let peripheral = {
        let state = state.lock().unwrap();
        state.peripherals.get(&address).cloned()
    };

    if let Some(peripheral) = peripheral {
        // 获取当前连接设备的服务
        let current_services: Vec<String> = peripheral
            .services()
            .iter()
            .map(|service| service.uuid.to_string())
            .collect();

        // 如果发现了新的服务，更新缓存
        if !current_services.is_empty() {
            let mut state_locked = state.lock().unwrap();
            state_locked.update_discovered_services(&address, current_services);
        }

        // 获取合并后的服务（包括广播服务和发现的服务）
        let state_locked = state.lock().unwrap();
        let merged_services = state_locked.get_device_services(&address);

        return Ok(merged_services);
    }

    // 如果设备未连接，尝试从缓存中获取服务
    let state_locked = state.lock().unwrap();
    let cached_services = state_locked.get_device_services(&address);

    if !cached_services.is_empty() {
        Ok(cached_services)
    } else {
        Err("Device not found and no cached services available".to_string())
    }
}

// 获取设备服务缓存信息（用于调试和监控）
#[tauri::command]
async fn get_service_cache_info(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<ServiceCacheInfo, String> {
    let state_locked = state.lock().unwrap();

    if let Some(cache) = state_locked.device_services.get(&address) {
        let timestamp = cache.last_updated
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Ok(ServiceCacheInfo {
            address,
            advertised_services: cache.advertised_services.clone(),
            discovered_services: cache.discovered_services.clone(),
            merged_services: cache.get_merged_services(),
            last_updated: timestamp,
        })
    } else {
        Err("No cache found for device".to_string())
    }
}

// 清理设备服务缓存
#[tauri::command]
async fn clear_service_cache(
    address: Option<String>,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let mut state_locked = state.lock().unwrap();

    match address {
        Some(addr) => {
            state_locked.device_services.remove(&addr);
            Ok(())
        }
        None => {
            state_locked.device_services.clear();
            Ok(())
        }
    }
}

// 获取所有缓存的设备地址
#[tauri::command]
async fn get_cached_devices(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let state_locked = state.lock().unwrap();
    let addresses: Vec<String> = state_locked.device_services.keys().cloned().collect();
    Ok(addresses)
}

// 监听扫描更新 - 通过事件系统发送
#[tauri::command]
async fn start_scan_listener(app_handle: AppHandle) -> Result<(), String> {
    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();

    {
        let state = state_guard.lock().unwrap();
        if let Some(scanning_tx) = &state.scanning_tx {
            let mut rx_clone = scanning_tx.subscribe();
            let app_handle_clone = app_handle.clone();

            // 启动监听任务
            tauri::async_runtime::spawn(async move {
                while let Ok(devices) = rx_clone.recv().await {
                    let _ = app_handle_clone.emit("scan-update", devices);
                }
            });
        }
    }

    Ok(())
}

// 监听连接更新 - 通过事件系统发送
#[tauri::command]
async fn start_connection_listener(app_handle: AppHandle) -> Result<(), String> {
    let (broadcast_tx, _) = broadcast::channel(16);

    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
    {
        let mut state = state_guard.lock().unwrap();
        state.connection_tx = Some(broadcast_tx.clone());
    }

    let mut broadcast_rx = broadcast_tx.subscribe();
    let app_handle_clone = app_handle.clone();

    // 启动监听任务
    tauri::async_runtime::spawn(async move {
        while let Ok(connected) = broadcast_rx.recv().await {
            let _ = app_handle_clone.emit("connection-update", connected);
        }
    });

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = Arc::new(Mutex::new(AppState::new()));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            get_adapter,
            start_scan,
            stop_scan,
            connect_device,
            disconnect_device,
            get_device_services,
            get_service_cache_info,
            clear_service_cache,
            get_cached_devices,
            start_scan_listener,
            start_connection_listener
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
