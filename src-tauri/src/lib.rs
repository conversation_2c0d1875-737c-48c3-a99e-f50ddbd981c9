//! BLE (Bluetooth Low Energy) 管理库
//!
//! 提供蓝牙设备扫描、连接、服务发现等功能的 Tauri 命令接口。
//! 支持设备服务缓存，合并广播服务和连接后发现的服务。

use btleplug::api::{Central, CentralEvent, <PERSON>r<PERSON><PERSON>F<PERSON><PERSON>, Manager as _, Peripheral as _, ScanFilter};
use btleplug::platform::{<PERSON><PERSON><PERSON>, Manager, Peripheral};
use futures_util::StreamExt;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager as _, State};
use tokio::sync::broadcast;
use uuid::Uuid;

// ================================================================================================
// 类型定义
// ================================================================================================

/// 扫描参数
#[derive(Debug, serde::Deserialize)]
struct StartScanArgs {
    #[serde(alias = "manufacturerId", alias = "manufacturer_id")]
    manufacturer_id: Option<u16>,
}

/// 连接参数
#[derive(Debug, serde::Deserialize)]
struct ConnectDeviceArgs {
    /// 设备地址
    address: String,
    /// 要订阅的服务特征值 UUID 列表
    #[serde(alias = "subscribeCharacteristics", alias = "subscribe_characteristics")]
    subscribe_characteristics: Option<Vec<String>>,
}

/// 特征值通知数据
#[derive(Debug, Clone, serde::Serialize)]
struct CharacteristicNotification {
    /// 设备地址
    device_address: String,
    /// 特征值 UUID
    characteristic_uuid: String,
    /// 通知数据
    data: Vec<u8>,
    /// 时间戳
    timestamp: u64,
}

/// BLE 设备信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct BleDevice {
    /// 设备名称
    pub name: String,
    /// 设备地址
    pub address: String,
    /// 信号强度
    pub rssi: i16,
    /// 是否已连接
    pub is_connected: bool,
    /// 厂商数据
    pub manufacturer_data: HashMap<String, Vec<u8>>,
    /// 服务列表
    pub services: Vec<String>,
}

impl BleDevice {
    /// 从蓝牙设备属性构建 BleDevice
    fn from_properties(
        id: &btleplug::platform::PeripheralId,
        properties: &btleplug::api::PeripheralProperties,
    ) -> Self {
        let address = id.to_string();
        let rssi = properties.rssi.unwrap_or(0);
        let manufacturer_data = properties
            .manufacturer_data
            .iter()
            .map(|(k, v)| (k.to_string(), v.clone()))
            .collect();
        let name = properties
            .local_name
            .clone()
            .unwrap_or_else(|| address.clone());
        let services: Vec<String> = properties.services.iter().map(|u| u.to_string()).collect();

        Self {
            name,
            address,
            rssi,
            is_connected: false,
            manufacturer_data,
            services,
        }
    }
}

/// 服务缓存信息（用于调试和监控）
#[derive(Debug, Clone, serde::Serialize)]
struct ServiceCacheInfo {
    address: String,
    advertised_services: Vec<String>,
    discovered_services: Vec<String>,
    merged_services: Vec<String>,
    last_updated: u64, // Unix timestamp
}

/// 设备服务缓存，用于合并广播服务和连接后发现的服务
#[derive(Debug, Clone)]
struct DeviceServiceCache {
    /// 广播中宣告的服务（扫描时获取）
    advertised_services: Vec<String>,
    /// 连接后发现的服务（GATT 服务发现）
    discovered_services: Vec<String>,
    /// 最后更新时间戳
    last_updated: std::time::SystemTime,
}

impl DeviceServiceCache {
    fn new() -> Self {
        Self {
            advertised_services: Vec::new(),
            discovered_services: Vec::new(),
            last_updated: std::time::SystemTime::now(),
        }
    }

    /// 更新广播服务
    fn update_advertised_services(&mut self, services: Vec<String>) {
        self.advertised_services = services;
        self.last_updated = std::time::SystemTime::now();
    }

    /// 更新发现的服务
    fn update_discovered_services(&mut self, services: Vec<String>) {
        self.discovered_services = services;
        self.last_updated = std::time::SystemTime::now();
    }

    /// 获取合并后的服务列表（去重并排序）
    fn get_merged_services(&self) -> Vec<String> {
        let mut merged = Vec::new();
        merged.extend(self.advertised_services.iter().cloned());
        merged.extend(self.discovered_services.iter().cloned());

        // 去重并排序
        merged.sort();
        merged.dedup();
        merged
    }

    /// 获取最佳可用的服务列表
    /// 优先返回发现的服务，如果为空则返回广播服务
    fn get_best_services(&self) -> Vec<String> {
        if !self.discovered_services.is_empty() {
            self.get_merged_services()
        } else if !self.advertised_services.is_empty() {
            self.advertised_services.clone()
        } else {
            Vec::new()
        }
    }
}

// ================================================================================================
// 应用状态管理
// ================================================================================================

/// 应用状态
struct AppState {
    /// 蓝牙适配器
    central: Option<Adapter>,
    /// 已发现的外设设备
    peripherals: HashMap<String, Peripheral>,
    /// 扫描结果广播发送器
    scanning_tx: Option<broadcast::Sender<Vec<BleDevice>>>,
    /// 连接状态广播发送器
    connection_tx: Option<broadcast::Sender<bool>>,
    /// 特征值通知广播发送器
    notification_tx: Option<broadcast::Sender<CharacteristicNotification>>,
    /// 设备服务缓存，key 为设备地址
    device_services: HashMap<String, DeviceServiceCache>,
}

impl AppState {
    /// 创建新的应用状态
    fn new() -> Self {
        Self {
            central: None,
            peripherals: HashMap::new(),
            scanning_tx: None,
            connection_tx: None,
            notification_tx: None,
            device_services: HashMap::new(),
        }
    }

    /// 更新设备的广播服务
    fn update_advertised_services(&mut self, address: &str, services: Vec<String>) {
        let cache = self
            .device_services
            .entry(address.to_string())
            .or_insert_with(DeviceServiceCache::new);
        cache.update_advertised_services(services);
    }

    /// 更新设备的发现服务
    fn update_discovered_services(&mut self, address: &str, services: Vec<String>) {
        let cache = self
            .device_services
            .entry(address.to_string())
            .or_insert_with(DeviceServiceCache::new);
        cache.update_discovered_services(services);
    }

    /// 获取设备的合并服务
    fn get_device_services(&self, address: &str) -> Vec<String> {
        self.device_services
            .get(address)
            .map(|cache| cache.get_best_services())
            .unwrap_or_default()
    }
}

// ================================================================================================
// Tauri 命令接口
// ================================================================================================

/// 测试命令
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// 获取蓝牙适配器
#[tauri::command]
async fn get_adapter() -> Result<(), String> {
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;

    if adapters.is_empty() {
        return Err("No Bluetooth adapters found".to_string());
    }

    Ok(())
}

// ================================================================================================
// 扫描相关命令
// ================================================================================================

/// 开始扫描设备
#[tauri::command]
async fn start_scan(
    app_handle: AppHandle,
    state: State<'_, Arc<Mutex<AppState>>>,
    args: Option<StartScanArgs>,
) -> Result<(), String> {
    let manufacturer_id = args.and_then(|a| a.manufacturer_id);

    // 初始化蓝牙管理器和适配器
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;
    let central = adapters
        .into_iter()
        .next()
        .ok_or("No Bluetooth adapters found".to_string())?;

    // 创建广播通道
    let (tx, _) = broadcast::channel(16);
    let tx_clone = tx.clone();

    // 更新应用状态
    {
        let mut state = state.lock().unwrap();
        state.central = Some(central.clone());
        state.scanning_tx = Some(tx);
        state.peripherals.clear();
    }

    // 启动扫描事件监听
    let mut events = central.events().await.map_err(|e| e.to_string())?;
    let central_clone = central.clone();
    let app_handle_clone = app_handle.clone();
    let vendor_filter = manufacturer_id;

    // 启动扫描任务
    tauri::async_runtime::spawn(async move {
        let mut device_map: HashMap<String, BleDevice> = HashMap::new();

        /// 处理设备属性，包括过滤、缓存和广播
        fn handle_device_properties(
            id: &btleplug::platform::PeripheralId,
            properties: &btleplug::api::PeripheralProperties,
            vendor_filter: Option<u16>,
            app_handle: &AppHandle,
            device_map: &mut HashMap<String, BleDevice>,
            tx: &broadcast::Sender<Vec<BleDevice>>,
        ) {
            // 厂商 ID 过滤
            if let Some(filter_id) = vendor_filter {
                if properties.manufacturer_data.is_empty()
                    || !properties.manufacturer_data.contains_key(&filter_id) {
                    return;
                }
            }

            let address = id.to_string();
            let mut device = BleDevice::from_properties(id, properties);
            let current_services = device.services.clone();

            // 更新广播服务缓存
            if !current_services.is_empty() {
                if let Some(state_guard) = app_handle.try_state::<Arc<Mutex<AppState>>>() {
                    if let Ok(mut state) = state_guard.lock() {
                        state.update_advertised_services(&address, current_services);
                    }
                }
            }

            // 获取合并后的服务
            let merged_services = if let Some(state_guard) = app_handle.try_state::<Arc<Mutex<AppState>>>() {
                if let Ok(state) = state_guard.lock() {
                    state.get_device_services(&address)
                } else {
                    Vec::new()
                }
            } else {
                Vec::new()
            };

            // 更新设备服务列表
            if !merged_services.is_empty() {
                device.services = merged_services;
            } else if device.services.is_empty() {
                // 尝试从之前的设备映射中获取服务
                if let Some(prev_device) = device_map.get(&address) {
                    device.services = prev_device.services.clone();
                }
            }

            // 更新设备映射并广播
            device_map.insert(address, device);
            let devices: Vec<BleDevice> = device_map.values().cloned().collect();
            let _ = tx.send(devices);
        }

        // 开始扫描
        if let Err(err) = central_clone.start_scan(ScanFilter::default()).await {
            eprintln!("Error starting scan: {:?}", err);
            return;
        }

        // 处理扫描到的设备
        while let Some(event) = events.next().await {
            match event {
                CentralEvent::DeviceDiscovered(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            // 先保存外设引用，便于后续 DeviceUpdated 使用
                            let address = id.to_string();
                            let state_guard = app_handle_clone.state::<Arc<Mutex<AppState>>>();
                            let mut state = state_guard.lock().unwrap();
                            state.peripherals.insert(address, peripheral);
                            drop(state);

                            handle_device_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                CentralEvent::DeviceUpdated(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            handle_device_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                _ => {}
            }
        }
    });

    Ok(())
}

/// 停止扫描
#[tauri::command]
async fn stop_scan(state: State<'_, Arc<Mutex<AppState>>>) -> Result<(), String> {
    let central = {
        let state = state.lock().unwrap();
        state.central.clone()
    };

    if let Some(central) = central {
        central.stop_scan().await.map_err(|e| e.to_string())?;
    }

    Ok(())
}

// ================================================================================================
// 设备连接相关命令
// ================================================================================================

/// 订阅指定特征值的通知
async fn subscribe_to_characteristics(
    peripheral: &Peripheral,
    characteristic_uuids: &[String],
    device_address: &str,
    app_handle: &AppHandle,
    notification_tx: Option<broadcast::Sender<CharacteristicNotification>>,
) -> Result<(), String> {
    // 解析 UUID 字符串
    let target_uuids: Result<Vec<Uuid>, _> = characteristic_uuids
        .iter()
        .map(|uuid_str| Uuid::parse_str(uuid_str))
        .collect();

    let target_uuids = target_uuids.map_err(|e| format!("Invalid UUID format: {}", e))?;

    // 查找并订阅特征值
    for characteristic in peripheral.characteristics() {
        println!("Checking characteristic: {:?}, UUID: {}", target_uuids.clone(), &characteristic.uuid);

        if target_uuids.contains(&characteristic.uuid)
            && characteristic.properties.contains(CharPropFlags::NOTIFY) {

            // println!("Subscribing to characteristic: {}", characteristic.uuid);

            // 订阅特征值
            peripheral.subscribe(&characteristic).await
                .map_err(|e| format!("Failed to subscribe to characteristic {}: {}", characteristic.uuid, e))?;

            // 启动通知监听任务
            if let Some(tx) = &notification_tx {
                let mut notification_stream = peripheral.notifications().await
                    .map_err(|e| format!("Failed to get notification stream: {}", e))?;

                let tx_clone = tx.clone();
                let app_handle_clone = app_handle.clone();
                let device_address_clone = device_address.to_string();
                let characteristic_uuid = characteristic.uuid;

                // 启动异步任务监听通知
                tauri::async_runtime::spawn(async move {
                    while let Some(notification) = notification_stream.next().await {
                        println!("Received notification: {:?}", notification.clone());
                        if notification.uuid == characteristic_uuid {
                            let notification_data = CharacteristicNotification {
                                device_address: device_address_clone.clone(),
                                characteristic_uuid: notification.uuid.to_string(),
                                data: notification.value,
                                timestamp: std::time::SystemTime::now()
                                    .duration_since(std::time::UNIX_EPOCH)
                                    .unwrap_or_default()
                                    .as_secs(),
                            };

                            println!("Received notification: {:?}", notification_data.clone());

                            // 广播通知数据
                            let _ = tx_clone.send(notification_data.clone());

                            // 发送到前端
                            let _ = app_handle_clone.emit("characteristic-notification", notification_data);
                        }
                    }
                });
            }
        }
    }

    Ok(())
}

/// 连接设备（兼容旧接口）
#[tauri::command]
async fn connect_device(
    address: String,
    app_handle: AppHandle,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let args = ConnectDeviceArgs {
        address,
        subscribe_characteristics: None,
    };
    connect_device_with_subscription(args, app_handle, state).await
}

/// 连接设备并订阅特征值
#[tauri::command]
async fn connect_device_with_subscription(
    args: ConnectDeviceArgs,
    app_handle: AppHandle,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let address = args.address;
    let subscribe_characteristics = args.subscribe_characteristics.unwrap_or_default();

    let (peripheral, connection_tx, notification_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        let notification_tx = state.notification_tx.clone();
        (peripheral, connection_tx, notification_tx)
    };

    let peripheral = peripheral.ok_or("Device not found")?;

    // 连接设备
    peripheral.connect().await.map_err(|e| e.to_string())?;

    // 发现服务
    peripheral.discover_services().await.map_err(|e| e.to_string())?;

    // 获取连接后发现的服务
    let discovered_services: Vec<String> = peripheral
        .services()
        .iter()
        .map(|s| s.uuid.to_string())
        .collect();

    // 更新设备的发现服务缓存
    {
        let mut state_locked = state.lock().unwrap();
        state_locked.update_discovered_services(&address, discovered_services);
    }

    // 订阅指定的特征值
    if !subscribe_characteristics.is_empty() {
        subscribe_to_characteristics(
            &peripheral,
            &subscribe_characteristics,
            &address,
            &app_handle,
            notification_tx,
        ).await?;
    }

    // 发送连接状态更新
    if let Some(tx) = &connection_tx {
        let _ = tx.send(true);
    }

    Ok(())
}

/// 断开连接
#[tauri::command]
async fn disconnect_device(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let (peripheral, connection_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        (peripheral, connection_tx)
    };

    let peripheral = peripheral.ok_or("Device not found")?;

    peripheral.disconnect().await.map_err(|e| e.to_string())?;

    // 发送连接状态更新
    if let Some(tx) = &connection_tx {
        let _ = tx.send(false);
    }

    Ok(())
}

// ================================================================================================
// 服务相关命令
// ================================================================================================

/// 获取设备服务
#[tauri::command]
async fn get_device_services(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let peripheral = {
        let state = state.lock().unwrap();
        state.peripherals.get(&address).cloned()
    };

    if let Some(peripheral) = peripheral {
        // 获取当前连接设备的服务
        let current_services: Vec<String> = peripheral
            .services()
            .iter()
            .map(|service| service.uuid.to_string())
            .collect();

        // 如果发现了新的服务，更新缓存
        if !current_services.is_empty() {
            let mut state_locked = state.lock().unwrap();
            state_locked.update_discovered_services(&address, current_services);
        }

        // 获取合并后的服务（包括广播服务和发现的服务）
        let state_locked = state.lock().unwrap();
        let merged_services = state_locked.get_device_services(&address);
        return Ok(merged_services);
    }

    // 如果设备未连接，尝试从缓存中获取服务
    let state_locked = state.lock().unwrap();
    let cached_services = state_locked.get_device_services(&address);

    if !cached_services.is_empty() {
        Ok(cached_services)
    } else {
        Err("Device not found and no cached services available".to_string())
    }
}

// ================================================================================================
// 缓存管理相关命令
// ================================================================================================

/// 获取设备服务缓存信息（用于调试和监控）
#[tauri::command]
async fn get_service_cache_info(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<ServiceCacheInfo, String> {
    let state_locked = state.lock().unwrap();

    let cache = state_locked
        .device_services
        .get(&address)
        .ok_or("No cache found for device")?;

    let timestamp = cache
        .last_updated
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    Ok(ServiceCacheInfo {
        address,
        advertised_services: cache.advertised_services.clone(),
        discovered_services: cache.discovered_services.clone(),
        merged_services: cache.get_merged_services(),
        last_updated: timestamp,
    })
}

/// 清理设备服务缓存
#[tauri::command]
async fn clear_service_cache(
    address: Option<String>,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let mut state_locked = state.lock().unwrap();

    match address {
        Some(addr) => {
            state_locked.device_services.remove(&addr);
        }
        None => {
            state_locked.device_services.clear();
        }
    }

    Ok(())
}

/// 获取所有缓存的设备地址
#[tauri::command]
async fn get_cached_devices(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let state_locked = state.lock().unwrap();
    let addresses: Vec<String> = state_locked.device_services.keys().cloned().collect();
    Ok(addresses)
}

// ================================================================================================
// 事件监听相关命令
// ================================================================================================

/// 监听扫描更新 - 通过事件系统发送
#[tauri::command]
async fn start_scan_listener(app_handle: AppHandle) -> Result<(), String> {
    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();

    let scanning_tx = {
        let state = state_guard.lock().unwrap();
        state.scanning_tx.clone()
    };

    if let Some(scanning_tx) = scanning_tx {
        let mut rx_clone = scanning_tx.subscribe();
        let app_handle_clone = app_handle.clone();

        // 启动监听任务
        tauri::async_runtime::spawn(async move {
            while let Ok(devices) = rx_clone.recv().await {
                let _ = app_handle_clone.emit("scan-update", devices);
            }
        });
    }

    Ok(())
}

/// 监听连接更新 - 通过事件系统发送
#[tauri::command]
async fn start_connection_listener(app_handle: AppHandle) -> Result<(), String> {
    let (broadcast_tx, _) = broadcast::channel(16);

    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
    {
        let mut state = state_guard.lock().unwrap();
        state.connection_tx = Some(broadcast_tx.clone());
    }

    let mut broadcast_rx = broadcast_tx.subscribe();
    let app_handle_clone = app_handle.clone();

    // 启动监听任务
    tauri::async_runtime::spawn(async move {
        while let Ok(connected) = broadcast_rx.recv().await {
            let _ = app_handle_clone.emit("connection-update", connected);
        }
    });

    Ok(())
}

/// 监听特征值通知 - 通过事件系统发送
#[tauri::command]
async fn start_notification_listener(app_handle: AppHandle) -> Result<(), String> {
    let (broadcast_tx, _) = broadcast::channel(16);

    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
    {
        let mut state = state_guard.lock().unwrap();
        state.notification_tx = Some(broadcast_tx.clone());
    }

    let mut broadcast_rx = broadcast_tx.subscribe();
    let app_handle_clone = app_handle.clone();

    // 启动监听任务
    tauri::async_runtime::spawn(async move {
        while let Ok(notification) = broadcast_rx.recv().await {
            let _ = app_handle_clone.emit("characteristic-notification", notification);
        }
    });

    Ok(())
}

// ================================================================================================
// 应用入口点
// ================================================================================================

/// Tauri 应用入口点
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = Arc::new(Mutex::new(AppState::new()));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            // 基础命令
            greet,
            get_adapter,
            // 扫描相关
            start_scan,
            stop_scan,
            start_scan_listener,
            // 连接相关
            connect_device,
            connect_device_with_subscription,
            disconnect_device,
            start_connection_listener,
            start_notification_listener,
            // 服务相关
            get_device_services,
            // 缓存管理
            get_service_cache_info,
            clear_service_cache,
            get_cached_devices,
        ])
        .run(tauri::generate_context!())
        .expect("Failed to run Tauri application");
}
