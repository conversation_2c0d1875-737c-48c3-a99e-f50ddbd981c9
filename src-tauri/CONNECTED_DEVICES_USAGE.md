# 处理应用重启后已连接设备的使用指南

## 问题描述

当蓝牙应用重启后，之前连接的设备在系统蓝牙中显示为已连接状态，但应用中无法搜索到这些设备。这是因为应用状态丢失，需要重新发现并恢复这些已连接的设备。

## 解决方案

### 1. 新增的 API 命令

#### `get_connected_devices`
获取系统中所有已连接的蓝牙设备，并恢复到应用状态中。

```typescript
// 前端调用示例
import { invoke } from '@tauri-apps/api/tauri';

interface BleDevice {
  name: string;
  address: string;
  rssi: number;
  is_connected: boolean;
  manufacturer_data: Record<string, number[]>;
  services: string[];
}

async function getConnectedDevices(): Promise<BleDevice[]> {
  try {
    const devices = await invoke<BleDevice[]>('get_connected_devices');
    console.log('已连接的设备:', devices);
    return devices;
  } catch (error) {
    console.error('获取已连接设备失败:', error);
    return [];
  }
}
```

#### `check_device_connection`
检查特定设备的连接状态。

```typescript
async function checkDeviceConnection(address: string): Promise<boolean> {
  try {
    const isConnected = await invoke<boolean>('check_device_connection', { address });
    console.log(`设备 ${address} 连接状态:`, isConnected);
    return isConnected;
  } catch (error) {
    console.error('检查设备连接状态失败:', error);
    return false;
  }
}
```

### 2. 应用启动时的推荐流程

```typescript
// 应用启动时的初始化流程
async function initializeBluetooth() {
  try {
    // 1. 获取蓝牙适配器
    await invoke('get_adapter');
    
    // 2. 启动扫描和连接监听器
    await invoke('start_scan_listener');
    await invoke('start_connection_listener');
    
    // 3. 获取已连接的设备
    const connectedDevices = await getConnectedDevices();
    
    // 4. 更新 UI 显示已连接的设备
    updateDeviceList(connectedDevices);
    
    // 5. 开始扫描新设备（会自动包含已连接的设备）
    await invoke('start_scan');
    
  } catch (error) {
    console.error('蓝牙初始化失败:', error);
  }
}
```

### 3. 扫描时自动包含已连接设备

现在 `start_scan` 命令已经优化，会自动在扫描开始时包含系统中已连接的设备：

```typescript
async function startScanning() {
  try {
    // 开始扫描，会自动包含已连接的设备
    await invoke('start_scan');
    
    // 监听扫描更新
    listen('scan-update', (event) => {
      const devices = event.payload as BleDevice[];
      console.log('扫描到的设备（包括已连接的）:', devices);
      updateDeviceList(devices);
    });
    
  } catch (error) {
    console.error('开始扫描失败:', error);
  }
}
```

### 4. Vue 组件使用示例

```vue
<template>
  <div class="bluetooth-manager">
    <v-btn @click="initBluetooth" :loading="initializing">
      初始化蓝牙
    </v-btn>
    
    <v-btn @click="refreshConnectedDevices" :loading="refreshing">
      刷新已连接设备
    </v-btn>
    
    <v-list>
      <v-list-item
        v-for="device in devices"
        :key="device.address"
        :class="{ 'connected-device': device.is_connected }"
      >
        <v-list-item-content>
          <v-list-item-title>{{ device.name }}</v-list-item-title>
          <v-list-item-subtitle>
            {{ device.address }} 
            <v-chip 
              :color="device.is_connected ? 'success' : 'default'"
              small
            >
              {{ device.is_connected ? '已连接' : '未连接' }}
            </v-chip>
          </v-list-item-subtitle>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/tauri'
import { listen } from '@tauri-apps/api/event'

const devices = ref<BleDevice[]>([])
const initializing = ref(false)
const refreshing = ref(false)

async function initBluetooth() {
  initializing.value = true
  try {
    await invoke('get_adapter')
    await invoke('start_scan_listener')
    await invoke('start_connection_listener')
    
    // 获取已连接的设备
    const connectedDevices = await invoke<BleDevice[]>('get_connected_devices')
    devices.value = connectedDevices
    
    // 开始扫描
    await invoke('start_scan')
    
    // 监听扫描更新
    listen('scan-update', (event) => {
      devices.value = event.payload as BleDevice[]
    })
    
  } catch (error) {
    console.error('蓝牙初始化失败:', error)
  } finally {
    initializing.value = false
  }
}

async function refreshConnectedDevices() {
  refreshing.value = true
  try {
    const connectedDevices = await invoke<BleDevice[]>('get_connected_devices')
    
    // 更新设备列表中的连接状态
    devices.value = devices.value.map(device => {
      const connectedDevice = connectedDevices.find(cd => cd.address === device.address)
      return connectedDevice || device
    })
    
    // 添加新发现的已连接设备
    connectedDevices.forEach(connectedDevice => {
      if (!devices.value.find(d => d.address === connectedDevice.address)) {
        devices.value.push(connectedDevice)
      }
    })
    
  } catch (error) {
    console.error('刷新已连接设备失败:', error)
  } finally {
    refreshing.value = false
  }
}

onMounted(() => {
  initBluetooth()
})
</script>

<style scoped>
.connected-device {
  background-color: rgba(76, 175, 80, 0.1);
}
</style>
```

### 5. 最佳实践

1. **应用启动时**：先调用 `get_connected_devices` 获取已连接设备
2. **定期检查**：可以定期调用 `check_device_connection` 检查设备连接状态
3. **扫描优化**：使用 `start_scan` 会自动包含已连接设备，无需额外处理
4. **错误处理**：始终包含适当的错误处理逻辑
5. **UI 反馈**：在 UI 中明确区分已连接和未连接的设备

### 6. 调试工具

使用服务缓存相关的命令来调试设备状态：

```typescript
// 获取设备服务缓存信息
const cacheInfo = await invoke('get_service_cache_info', { address: 'device_address' })
console.log('设备服务缓存:', cacheInfo)

// 获取所有缓存的设备
const cachedDevices = await invoke<string[]>('get_cached_devices')
console.log('已缓存的设备:', cachedDevices)
```

这个解决方案确保了应用重启后能够正确恢复已连接设备的状态，提供了更好的用户体验。
