/**
 * 特征值订阅功能测试示例
 * 
 * 这个示例展示了如何使用新的特征值订阅功能
 */

// 模拟前端调用（实际使用时需要在 Tauri 应用中运行）

// 1. 基本连接（兼容旧接口）
async function basicConnect() {
  try {
    await invoke('connect_device', {
      address: 'AA:BB:CC:DD:EE:FF'
    });
    console.log('基本连接成功');
  } catch (error) {
    console.error('连接失败:', error);
  }
}

// 2. 连接并订阅特征值
async function connectWithSubscription() {
  try {
    await invoke('connect_device_with_subscription', {
      args: {
        address: 'AA:BB:CC:DD:EE:FF',
        subscribeCharacteristics: [
          '6e400003-b534-f393-67a9-e50e24dcca9e', // Nordic UART TX
          '00002a37-0000-1000-8000-00805f9b34fb'  // Heart Rate Measurement
        ]
      }
    });
    console.log('连接并订阅成功');
  } catch (error) {
    console.error('连接订阅失败:', error);
  }
}

// 3. 启动通知监听器
async function startListeners() {
  try {
    // 启动各种监听器
    await invoke('start_scan_listener');
    await invoke('start_connection_listener');
    await invoke('start_notification_listener');
    
    console.log('所有监听器已启动');
  } catch (error) {
    console.error('启动监听器失败:', error);
  }
}

// 4. 监听事件
function setupEventListeners() {
  // 监听扫描更新
  listen('scan-update', (event) => {
    console.log('扫描到设备:', event.payload);
  });

  // 监听连接状态
  listen('connection-update', (event) => {
    console.log('连接状态更新:', event.payload);
  });

  // 监听特征值通知
  listen('characteristic-notification', (event) => {
    const notification = event.payload;
    console.log('收到特征值通知:', {
      设备: notification.device_address,
      特征值: notification.characteristic_uuid,
      数据: notification.data.map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
      时间: new Date(notification.timestamp * 1000).toLocaleString()
    });
  });
}

// 5. 完整的测试流程
async function fullTest() {
  console.log('开始完整测试流程...');
  
  try {
    // 1. 设置事件监听
    setupEventListeners();
    
    // 2. 启动监听器
    await startListeners();
    
    // 3. 获取蓝牙适配器
    await invoke('get_adapter');
    console.log('蓝牙适配器获取成功');
    
    // 4. 开始扫描
    await invoke('start_scan');
    console.log('开始扫描设备...');
    
    // 等待一段时间让设备被发现
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 5. 停止扫描
    await invoke('stop_scan');
    console.log('停止扫描');
    
    // 6. 连接设备并订阅特征值（需要替换为实际的设备地址）
    // await connectWithSubscription();
    
    console.log('测试流程完成');
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 6. 数据处理工具函数
function parseNotificationData(data, type = 'hex') {
  switch (type) {
    case 'hex':
      return data.map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' ');
    
    case 'ascii':
      return String.fromCharCode(...data);
    
    case 'utf8':
      return new TextDecoder('utf-8').decode(new Uint8Array(data));
    
    case 'int16':
      if (data.length >= 2) {
        return new DataView(new Uint8Array(data).buffer).getInt16(0, true); // little-endian
      }
      return null;
    
    case 'uint16':
      if (data.length >= 2) {
        return new DataView(new Uint8Array(data).buffer).getUint16(0, true); // little-endian
      }
      return null;
    
    case 'float32':
      if (data.length >= 4) {
        return new DataView(new Uint8Array(data).buffer).getFloat32(0, true); // little-endian
      }
      return null;
    
    default:
      return data;
  }
}

// 7. 心率数据解析示例
function parseHeartRateData(data) {
  if (data.length < 2) return null;
  
  const flags = data[0];
  const is16Bit = (flags & 0x01) !== 0;
  
  let heartRate;
  if (is16Bit) {
    heartRate = (data[2] << 8) | data[1];
  } else {
    heartRate = data[1];
  }
  
  return {
    heartRate,
    sensorContact: (flags & 0x06) === 0x06,
    energyExpended: (flags & 0x08) !== 0,
    rrInterval: (flags & 0x10) !== 0
  };
}

// 8. Nordic UART 数据解析示例
function parseNordicUartData(data) {
  try {
    return new TextDecoder('utf-8').decode(new Uint8Array(data));
  } catch (error) {
    console.error('Nordic UART 数据解析失败:', error);
    return parseNotificationData(data, 'hex');
  }
}

// 使用示例
console.log('特征值订阅测试示例已加载');
console.log('可用函数:');
console.log('- basicConnect(): 基本连接测试');
console.log('- connectWithSubscription(): 连接并订阅测试');
console.log('- startListeners(): 启动监听器');
console.log('- setupEventListeners(): 设置事件监听');
console.log('- fullTest(): 完整测试流程');
console.log('- parseNotificationData(data, type): 数据解析工具');
console.log('- parseHeartRateData(data): 心率数据解析');
console.log('- parseNordicUartData(data): Nordic UART 数据解析');

// 导出函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    basicConnect,
    connectWithSubscription,
    startListeners,
    setupEventListeners,
    fullTest,
    parseNotificationData,
    parseHeartRateData,
    parseNordicUartData
  };
}
