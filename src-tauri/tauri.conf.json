{"$schema": "https://schema.tauri.app/config/2", "productName": "ble-manage", "version": "0.1.0", "identifier": "com.simo.bleManage", "build": {"beforeDevCommand": "bash -lc 'source ~/.nvm/nvm.sh && nvm use 22.12.0 && pnpm dev'", "devUrl": "http://localhost:3000", "beforeBuildCommand": "bash -lc 'source ~/.nvm/nvm.sh && nvm use 22.12.0 && pnpm build'", "frontendDist": "../dist"}, "app": {"windows": [{"title": "", "width": 1200, "height": 900}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}