# 特征值订阅功能使用指南

## 功能概述

新增的特征值订阅功能允许在连接设备时自动订阅指定的特征值，并实时接收通知数据。这对于需要持续监听设备数据的应用场景非常有用。

## 新增的 API 命令

### 1. `connect_device_with_subscription`

连接设备并订阅指定的特征值。

```typescript
interface ConnectDeviceArgs {
  address: string;
  subscribeCharacteristics?: string[]; // 特征值 UUID 列表
}

// 前端调用示例
import { invoke } from '@tauri-apps/api/tauri';

async function connectWithSubscription(deviceAddress: string, characteristicUuids: string[]) {
  try {
    await invoke('connect_device_with_subscription', {
      args: {
        address: deviceAddress,
        subscribeCharacteristics: characteristicUuids
      }
    });
    console.log('设备连接成功并已订阅特征值');
  } catch (error) {
    console.error('连接失败:', error);
  }
}
```

### 2. `start_notification_listener`

启动特征值通知监听器。

```typescript
async function startNotificationListener() {
  try {
    await invoke('start_notification_listener');
    console.log('通知监听器已启动');
  } catch (error) {
    console.error('启动通知监听器失败:', error);
  }
}
```

## 事件监听

### 特征值通知事件

```typescript
import { listen } from '@tauri-apps/api/event';

interface CharacteristicNotification {
  device_address: string;
  characteristic_uuid: string;
  data: number[];
  timestamp: number;
}

// 监听特征值通知
listen<CharacteristicNotification>('characteristic-notification', (event) => {
  const notification = event.payload;
  console.log('收到特征值通知:', {
    设备地址: notification.device_address,
    特征值UUID: notification.characteristic_uuid,
    数据: notification.data,
    时间戳: new Date(notification.timestamp * 1000)
  });
});
```

## 完整使用示例

### Vue 3 示例

```vue
<template>
  <div class="ble-subscription-demo">
    <h2>BLE 特征值订阅示例</h2>
    
    <div class="connection-section">
      <input 
        v-model="deviceAddress" 
        placeholder="设备地址"
        class="input-field"
      />
      
      <div class="characteristics-input">
        <h3>要订阅的特征值 UUID:</h3>
        <div v-for="(uuid, index) in characteristicUuids" :key="index" class="uuid-input">
          <input 
            v-model="characteristicUuids[index]" 
            placeholder="特征值 UUID"
            class="input-field"
          />
          <button @click="removeCharacteristic(index)" class="remove-btn">删除</button>
        </div>
        <button @click="addCharacteristic" class="add-btn">添加特征值</button>
      </div>
      
      <div class="action-buttons">
        <button @click="connectWithSubscription" :disabled="connecting" class="connect-btn">
          {{ connecting ? '连接中...' : '连接并订阅' }}
        </button>
        <button @click="disconnect" :disabled="!connected" class="disconnect-btn">
          断开连接
        </button>
      </div>
    </div>

    <div class="notifications-section">
      <h3>实时通知数据:</h3>
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
        >
          <div class="notification-header">
            <span class="device-address">{{ notification.device_address }}</span>
            <span class="timestamp">{{ formatTimestamp(notification.timestamp) }}</span>
          </div>
          <div class="characteristic-uuid">{{ notification.characteristic_uuid }}</div>
          <div class="data">数据: {{ formatData(notification.data) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { invoke } from '@tauri-apps/api/tauri'
import { listen, type UnlistenFn } from '@tauri-apps/api/event'

interface CharacteristicNotification {
  device_address: string
  characteristic_uuid: string
  data: number[]
  timestamp: number
  id?: string
}

const deviceAddress = ref('')
const characteristicUuids = ref([''])
const connecting = ref(false)
const connected = ref(false)
const notifications = ref<CharacteristicNotification[]>([])

let unlistenNotification: UnlistenFn | null = null

// 添加特征值输入框
function addCharacteristic() {
  characteristicUuids.value.push('')
}

// 删除特征值输入框
function removeCharacteristic(index: number) {
  characteristicUuids.value.splice(index, 1)
}

// 连接并订阅
async function connectWithSubscription() {
  if (!deviceAddress.value) {
    alert('请输入设备地址')
    return
  }

  const validUuids = characteristicUuids.value.filter(uuid => uuid.trim() !== '')
  if (validUuids.length === 0) {
    alert('请至少添加一个特征值 UUID')
    return
  }

  connecting.value = true
  try {
    await invoke('connect_device_with_subscription', {
      args: {
        address: deviceAddress.value,
        subscribeCharacteristics: validUuids
      }
    })
    connected.value = true
    console.log('设备连接成功并已订阅特征值')
  } catch (error) {
    console.error('连接失败:', error)
    alert(`连接失败: ${error}`)
  } finally {
    connecting.value = false
  }
}

// 断开连接
async function disconnect() {
  try {
    await invoke('disconnect_device', { address: deviceAddress.value })
    connected.value = false
    console.log('设备已断开连接')
  } catch (error) {
    console.error('断开连接失败:', error)
  }
}

// 格式化时间戳
function formatTimestamp(timestamp: number): string {
  return new Date(timestamp * 1000).toLocaleTimeString()
}

// 格式化数据
function formatData(data: number[]): string {
  return data.map(byte => `0x${byte.toString(16).padStart(2, '0')}`).join(' ')
}

// 初始化
onMounted(async () => {
  try {
    // 启动通知监听器
    await invoke('start_notification_listener')
    
    // 监听特征值通知
    unlistenNotification = await listen<CharacteristicNotification>('characteristic-notification', (event) => {
      const notification = { ...event.payload, id: Date.now().toString() }
      notifications.value.unshift(notification)
      
      // 限制通知列表长度
      if (notifications.value.length > 100) {
        notifications.value = notifications.value.slice(0, 100)
      }
    })
    
  } catch (error) {
    console.error('初始化失败:', error)
  }
})

// 清理
onUnmounted(() => {
  if (unlistenNotification) {
    unlistenNotification()
  }
})
</script>

<style scoped>
.ble-subscription-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.connection-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.characteristics-input {
  margin: 15px 0;
}

.uuid-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  flex: 1;
}

.add-btn, .remove-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn {
  background-color: #4CAF50;
  color: white;
}

.remove-btn {
  background-color: #f44336;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.connect-btn, .disconnect-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.connect-btn {
  background-color: #2196F3;
  color: white;
}

.disconnect-btn {
  background-color: #ff9800;
  color: white;
}

.connect-btn:disabled, .disconnect-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.notifications-section {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: 5px;
}

.device-address {
  color: #2196F3;
}

.timestamp {
  color: #666;
  font-size: 0.9em;
}

.characteristic-uuid {
  color: #4CAF50;
  font-family: monospace;
  margin-bottom: 5px;
}

.data {
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 5px;
  border-radius: 3px;
}
</style>
```

## 常见特征值 UUID

以下是一些常见的 BLE 特征值 UUID，可用于测试：

```typescript
// 常见的 BLE 特征值 UUID
const COMMON_CHARACTERISTIC_UUIDS = {
  // 心率测量
  HEART_RATE_MEASUREMENT: '00002a37-0000-1000-8000-00805f9b34fb',
  
  // 电池电量
  BATTERY_LEVEL: '00002a19-0000-1000-8000-00805f9b34fb',
  
  // 设备信息
  DEVICE_NAME: '00002a00-0000-1000-8000-00805f9b34fb',
  MANUFACTURER_NAME: '00002a29-0000-1000-8000-00805f9b34fb',
  
  // Nordic UART Service
  NORDIC_UART_RX: '6e400002-b534-f393-67a9-e50e24dcca9e',
  NORDIC_UART_TX: '6e400003-b534-f393-67a9-e50e24dcca9e',
}
```

## 注意事项

1. **特征值必须支持通知**: 只有具有 NOTIFY 属性的特征值才能被订阅
2. **UUID 格式**: 确保 UUID 格式正确，支持标准的 128 位 UUID 格式
3. **连接状态**: 设备必须先连接成功才能订阅特征值
4. **资源管理**: 断开连接时会自动取消所有订阅
5. **错误处理**: 建议添加适当的错误处理逻辑

## 兼容性

- 保持与现有 `connect_device` 命令的兼容性
- 新的订阅功能是可选的，不影响现有代码
- 支持同时订阅多个特征值
