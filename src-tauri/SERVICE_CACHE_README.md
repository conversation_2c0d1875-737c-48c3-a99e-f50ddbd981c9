# 蓝牙设备服务缓存优化

## 概述

本次优化重构了蓝牙设备服务处理逻辑，引入了更清晰的服务缓存机制，解决了 macOS 上 HID 服务被隐藏的问题，并提供了更好的服务合并策略。

## 主要改进

### 1. 新的服务缓存结构

```rust
struct DeviceServiceCache {
    /// 广播中宣告的服务（扫描时获取）
    advertised_services: Vec<String>,
    /// 连接后发现的服务（GATT 服务发现）
    discovered_services: Vec<String>,
    /// 最后更新时间戳
    last_updated: std::time::SystemTime,
}
```

### 2. 清晰的服务合并逻辑

- **广播服务**: 在设备扫描阶段获取，通过广播包中的服务 UUID 列表
- **发现服务**: 在设备连接后通过 GATT 服务发现获取
- **合并策略**: 优先使用发现的服务，如果为空则使用广播服务，最终返回去重排序后的合并列表

### 3. 优化的 AppState

```rust
struct AppState {
    central: Option<Adapter>,
    peripherals: HashMap<String, Peripheral>,
    scanning_tx: Option<broadcast::Sender<Vec<BleDevice>>>,
    connection_tx: Option<broadcast::Sender<bool>>,
    /// 设备服务缓存，key 为设备地址
    device_services: HashMap<String, DeviceServiceCache>,
}
```

## 新增的 API 命令

### 1. `get_service_cache_info`
获取设备服务缓存的详细信息，用于调试和监控：
```rust
async fn get_service_cache_info(address: String) -> Result<ServiceCacheInfo, String>
```

返回结构：
```rust
struct ServiceCacheInfo {
    address: String,
    advertised_services: Vec<String>,
    discovered_services: Vec<String>,
    merged_services: Vec<String>,
    last_updated: u64, // Unix timestamp
}
```

### 2. `clear_service_cache`
清理设备服务缓存：
```rust
async fn clear_service_cache(address: Option<String>) -> Result<(), String>
```
- `address` 为 `Some(addr)`: 清理指定设备的缓存
- `address` 为 `None`: 清理所有设备的缓存

### 3. `get_cached_devices`
获取所有已缓存服务的设备地址列表：
```rust
async fn get_cached_devices() -> Result<Vec<String>, String>
```

## 核心方法说明

### DeviceServiceCache 方法

- `update_advertised_services()`: 更新广播服务列表
- `update_discovered_services()`: 更新发现的服务列表
- `get_merged_services()`: 获取合并后的服务列表（去重排序）
- `get_best_services()`: 获取最佳可用的服务列表

### AppState 方法

- `update_advertised_services()`: 更新设备的广播服务
- `update_discovered_services()`: 更新设备的发现服务
- `get_device_services()`: 获取设备的合并服务

## 使用场景

### 1. 扫描阶段
设备广播包中包含的服务会被缓存为 `advertised_services`

### 2. 连接阶段
连接后通过 GATT 发现的服务会被缓存为 `discovered_services`

### 3. 服务查询
`get_device_services` 返回合并后的服务列表，确保不丢失任何服务信息

## 兼容性说明

- **Windows**: 通常可以直接读取到所有服务，包括 HID (0x1812)
- **macOS**: CoreBluetooth 可能隐藏某些 GATT 服务，通过广播服务缓存提供兜底策略

## 调试建议

使用 `get_service_cache_info` 命令可以查看设备的服务缓存状态，帮助诊断服务发现问题：

```javascript
// 前端调用示例
const cacheInfo = await invoke('get_service_cache_info', { address: 'device_address' });
console.log('广播服务:', cacheInfo.advertised_services);
console.log('发现服务:', cacheInfo.discovered_services);
console.log('合并服务:', cacheInfo.merged_services);
```
