[package]
name = "ble-manage"
version = "0.1.0"
description = "Simotech Ble Manager"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "ble_manage_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
# 移除 tauri_plugin_blec 依赖，添加 btleplug 及相关依赖
btleplug = { version = "0.11.8", features = ["serde"] }
async-trait = "0.1"
tokio = { version = "1", features = ["sync", "rt-multi-thread", "time"] }
futures-util = { version = "0.3", default-features = false, features = ["alloc"] }

