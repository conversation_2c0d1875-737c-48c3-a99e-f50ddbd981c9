# 前端调用修改指南

## 概述

本指南详细说明了如何修改前端代码以使用新的特征值订阅功能。新功能完全向后兼容，现有代码无需修改即可继续工作。

## 主要修改内容

### 1. 类型定义更新

```typescript
// 新增特征值通知类型
interface CharacteristicNotification {
  device_address: string;
  characteristic_uuid: string;
  data: number[];
  timestamp: number;
}

// 新增连接参数类型
interface ConnectDeviceArgs {
  address: string;
  subscribeCharacteristics?: string[];
}
```

### 2. 新增 API 调用

#### 基本连接（保持兼容）
```typescript
// 原有方式，无需修改
await invoke('connect_device', { address: 'AA:BB:CC:DD:EE:FF' });
```

#### 连接并订阅特征值
```typescript
// 新的订阅方式
await invoke('connect_device_with_subscription', {
  args: {
    address: 'AA:BB:CC:DD:EE:FF',
    subscribeCharacteristics: [
      '6e400003-b534-f393-67a9-e50e24dcca9e', // Nordic UART TX
      '00002a37-0000-1000-8000-00805f9b34fb'  // Heart Rate Measurement
    ]
  }
});
```

#### 启动通知监听器
```typescript
// 在应用初始化时调用
await invoke('start_notification_listener');
```

### 3. 事件监听更新

```typescript
// 监听特征值通知
const notificationListener = await listen<CharacteristicNotification>(
  'characteristic-notification', 
  (event) => {
    const notification = event.payload;
    console.log('收到通知:', {
      设备: notification.device_address,
      特征值: notification.characteristic_uuid,
      数据: notification.data,
      时间: new Date(notification.timestamp * 1000)
    });
  }
);
```

## 实际修改示例

### 原有的 index.vue 修改要点

1. **添加新的状态变量**
```typescript
// 特征值订阅相关状态
const subscribeCharacteristics = ref<string[]>(['']);
const notifications = ref<CharacteristicNotification[]>([]);
const showSubscriptionDialog = ref(false);
let notificationListener: (() => void) | null = null;
```

2. **更新初始化函数**
```typescript
onMounted(async () => {
  try {
    await invoke("get_adapter");
    await invoke("start_connection_listener");
    await invoke("start_notification_listener"); // 新增

    // 监听特征值通知 - 新增
    notificationListener = await listen<CharacteristicNotification>(
      "characteristic-notification", 
      (event) => {
        notifications.value.unshift(event.payload);
        // 限制通知列表长度
        if (notifications.value.length > 100) {
          notifications.value = notifications.value.slice(0, 100);
        }
      }
    );
  } catch (error) {
    console.error("Error initializing Bluetooth:", error);
  }
});
```

3. **添加新的连接函数**
```typescript
// 连接并订阅特征值
async function handleConnectWithSubscription() {
  if (!chooseDevice.value) return;

  const validUuids = subscribeCharacteristics.value.filter(uuid => uuid.trim() !== '');
  if (validUuids.length === 0) {
    alert('请至少添加一个特征值 UUID');
    return;
  }

  try {
    const args: ConnectDeviceArgs = {
      address: chooseDevice.value.address,
      subscribeCharacteristics: validUuids
    };

    await invoke("connect_device_with_subscription", { args });
    chooseDevice.value.is_connected = true;
    showSubscriptionDialog.value = false;
  } catch (error) {
    console.error("Connection with subscription error:", error);
  }
}
```

4. **更新模板**
```vue
<!-- 添加订阅连接按钮 -->
<button
  v-if="!chooseDevice?.is_connected"
  class="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
  @click="showSubscriptionDialog = true"
>
  订阅连接
</button>

<!-- 添加特征值订阅对话框 -->
<div v-if="showSubscriptionDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <!-- 对话框内容 -->
</div>

<!-- 添加通知显示区域 -->
<div v-if="notifications.length > 0" class="fixed bottom-4 right-4 w-80 max-h-60 bg-white border rounded-lg shadow-lg">
  <!-- 通知列表 -->
</div>
```

## 推荐的组合式函数方式

为了更好的代码组织，推荐使用 `useBLE` 组合式函数：

### 1. 创建 `composables/useBLE.ts`

```typescript
import { ref, onMounted, onUnmounted } from 'vue';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export function useBLE() {
  const devices = ref<BleDevice[]>([]);
  const notifications = ref<CharacteristicNotification[]>([]);
  const isScanning = ref(false);
  const isConnected = ref(false);

  // 初始化、连接、订阅等方法...
  
  return {
    devices,
    notifications,
    isScanning,
    isConnected,
    startScan,
    stopScan,
    connectDevice,
    connectDeviceWithSubscription,
    // ... 其他方法
  };
}
```

### 2. 在组件中使用

```vue
<script setup lang="ts">
import { useBLE } from '@/composables/useBLE';

const {
  devices,
  notifications,
  isScanning,
  connectDeviceWithSubscription,
  // ... 其他需要的方法和状态
} = useBLE();
</script>
```

## 常用特征值 UUID

```typescript
export const CHARACTERISTIC_UUIDS = {
  // Nordic UART Service
  NORDIC_UART_RX: '6e400002-b534-f393-67a9-e50e24dcca9e',
  NORDIC_UART_TX: '6e400003-b534-f393-67a9-e50e24dcca9e',
  
  // 标准 BLE 服务
  HEART_RATE_MEASUREMENT: '00002a37-0000-1000-8000-00805f9b34fb',
  BATTERY_LEVEL: '00002a19-0000-1000-8000-00805f9b34fb',
  DEVICE_NAME: '00002a00-0000-1000-8000-00805f9b34fb',
  MANUFACTURER_NAME: '00002a29-0000-1000-8000-00805f9b34fb',
} as const;
```

## 数据解析工具

```typescript
// 格式化数据为十六进制
function formatData(data: number[]): string {
  return data.map(byte => `0x${byte.toString(16).padStart(2, '0')}`).join(' ');
}

// 解析心率数据
function parseHeartRateData(data: number[]) {
  if (data.length < 2) return null;
  const flags = data[0];
  const is16Bit = (flags & 0x01) !== 0;
  const heartRate = is16Bit ? (data[2] << 8) | data[1] : data[1];
  return { heartRate, sensorContact: (flags & 0x06) === 0x06 };
}

// 解析 Nordic UART 数据
function parseNordicUartData(data: number[]): string {
  try {
    return new TextDecoder('utf-8').decode(new Uint8Array(data));
  } catch (error) {
    return formatData(data);
  }
}
```

## 迁移步骤

1. **保持现有代码不变** - 所有现有功能继续正常工作
2. **添加新的类型定义** - 在需要使用新功能的地方添加类型
3. **更新初始化代码** - 添加 `start_notification_listener` 调用
4. **添加通知监听** - 监听 `characteristic-notification` 事件
5. **添加新的 UI 组件** - 特征值订阅对话框和通知显示
6. **测试功能** - 确保新旧功能都正常工作

## 注意事项

1. **向后兼容** - 现有的 `connect_device` 调用无需修改
2. **错误处理** - 添加适当的错误处理和用户反馈
3. **性能考虑** - 限制通知历史长度，避免内存泄漏
4. **用户体验** - 提供清晰的 UI 反馈和操作指引
5. **资源清理** - 在组件卸载时清理事件监听器

## 完整示例文件

- `src/examples/frontend-usage-example.ts` - 完整的 TypeScript 使用示例
- `src/composables/useBLE.ts` - Vue 3 组合式函数
- `src/examples/improved-index.vue` - 改进后的页面示例

这些文件提供了完整的实现参考，可以直接使用或根据需要进行调整。
