<template>
  <div class="flex flex-col h-screen bg-white rounded-2xl shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h1 class="flex items-center gap-2 text-lg font-semibold">
        <span class="icon-[mdi--bluetooth-audio] text-2xl text-blue-500" />
        <span class="text-blue-500">蓝牙管理</span>
      </h1>
      <div class="flex items-center gap-3">
        <v-btn circle icon="mdi-cog-outline" size="large" />
        <v-btn circle icon="mdi-refresh" size="large" />

        <v-switch
          color="primary"
          :label="scanning ? '停止搜索' : '开始搜索'"
          :model-value="scanning"
          @update:model-value="handleUpdateSwitch"
        />
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b text-red-500">
      <h2 class="font-semibold text-red-500">已连接设备</h2>
    </div>

    <!-- Body -->
    <div class="flex flex-1 divide-x">
      <!-- Nearby Devices -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">附近设备</h2>
        <div class="space-y-3 h-[calc(100dvh-200px)] overflow-y-auto">
          <template v-for="(bleDevice, key) in deviceList" :key="key">
            <div
              class="p-3 border rounded-xl hover:bg-gray-50"
              @click="chooseDevice = bleDevice"
            >
              <div class="flex justify-between items-center">
                <p class="font-medium">{{ bleDevice.name }}</p>
                <p v-if="bleDevice.is_connected" class="text-green-600 text-sm">
                  •已连接
                </p>
              </div>
              <div class="flex gap-6">
                <p class="w-1/2 overflow-hidden">
                  <span class="30">&nbsp;&nbsp;{{ bleDevice.address }}</span>
                </p>
                <p class="w-1/2 text-sm">信号强度: {{ bleDevice.rssi }}</p>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Device Details -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">设备详情</h2>
        <div v-if="chooseDevice" class="p-4 border rounded-xl">
          <p>
            <span class="font-medium">设备名称:</span> {{ chooseDevice?.name }}
          </p>
          <p>
            <span class="font-medium">状态:</span>
            {{ chooseDevice?.is_connected ? "已连接" : "未连接" }}
          </p>
          <p><span class="font-medium">电量:</span> 80%</p>
          <p><span class="font-medium">服务:</span></p>
          <p v-for="(service, key) in chooseDevice?.services" :key="key">
            {{ service }}
          </p>
          <div class="flex gap-2 mt-4">
            <button
              class="px-3 py-1 border rounded-lg hover:bg-gray-100"
              @click="handleConnectWithSubscription"
            >
              {{ chooseDevice?.is_connected ? "断开连接" : "连接设备" }}
            </button>
            <button
              v-if="!chooseDevice?.is_connected"
              class="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              @click="showSubscriptionDialog = true"
            >
              订阅连接
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 特征值订阅对话框 -->
    <div v-if="showSubscriptionDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto">
        <h3 class="text-lg font-semibold mb-4">连接并订阅特征值</h3>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">设备: {{ chooseDevice?.name }}</p>
          <p class="text-sm text-gray-600 mb-4">地址: {{ chooseDevice?.address }}</p>
        </div>

        <div class="mb-4">
          <h4 class="font-medium mb-2">要订阅的特征值 UUID:</h4>
          <div v-for="(uuid, index) in subscribeCharacteristics" :key="index" class="flex gap-2 mb-2">
            <input
              v-model="subscribeCharacteristics[index]"
              placeholder="例如: 6e400003-b534-f393-67a9-e50e24dcca9e"
              class="flex-1 px-3 py-2 border rounded-lg text-sm"
            />
            <button
              @click="removeCharacteristic(index)"
              class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 text-sm"
            >
              删除
            </button>
          </div>
          <button
            @click="addCharacteristic"
            class="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 text-sm"
          >
            添加特征值
          </button>
        </div>

        <div class="mb-4">
          <h4 class="font-medium mb-2">常用特征值:</h4>
          <div class="space-y-1 text-sm">
            <button
              @click="subscribeCharacteristics[0] = '6e400003-b534-f393-67a9-e50e24dcca9e'"
              class="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
            >
              Nordic UART TX
            </button>
            <button
              @click="subscribeCharacteristics[0] = '00002a37-0000-1000-8000-00805f9b34fb'"
              class="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
            >
              心率测量
            </button>
            <button
              @click="subscribeCharacteristics[0] = '00002a19-0000-1000-8000-00805f9b34fb'"
              class="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
            >
              电池电量
            </button>
          </div>
        </div>

        <div class="flex gap-2">
          <button
            @click="handleConnectWithSubscription"
            class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            连接并订阅
          </button>
          <button
            @click="showSubscriptionDialog = false"
            class="px-4 py-2 border rounded-lg hover:bg-gray-100"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 通知显示区域 -->
    <div v-if="notifications.length > 0" class="fixed bottom-4 right-4 w-80 max-h-60 bg-white border rounded-lg shadow-lg overflow-hidden">
      <div class="p-3 bg-gray-50 border-b">
        <h4 class="font-medium">实时通知</h4>
        <button
          @click="notifications = []"
          class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      <div class="overflow-y-auto max-h-48">
        <div
          v-for="notification in notifications.slice(0, 10)"
          :key="notification.timestamp"
          class="p-2 border-b text-xs"
        >
          <div class="flex justify-between items-center mb-1">
            <span class="font-medium text-blue-600">{{ notification.device_address.slice(-8) }}</span>
            <span class="text-gray-500">{{ formatTimestamp(notification.timestamp) }}</span>
          </div>
          <div class="text-green-600 mb-1">{{ notification.characteristic_uuid.slice(0, 8) }}...</div>
          <div class="font-mono bg-gray-100 p-1 rounded">{{ formatNotificationData(notification.data) }}</div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <!-- <div class="flex justify-between items-center p-3 border-t text-sm text-gray-600">
      <span>Adapter: Intel Wireless Bluetooth v5.3</span>
      <span>Connected: 2 devices</span>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { listen } from "@tauri-apps/api/event";
import { invoke } from "@tauri-apps/api/core";
import { onMounted, onUnmounted, ref } from "vue";

// 定义设备类型
interface BleDevice {
  name: string;
  address: string;
  rssi: number;
  is_connected: boolean;
  manufacturer_data: Record<string, number[]>;
  services: string[];
}

// 定义特征值通知类型
interface CharacteristicNotification {
  device_address: string;
  characteristic_uuid: string;
  data: number[];
  timestamp: number;
}

// 定义连接参数类型
interface ConnectDeviceArgs {
  address: string;
  subscribeCharacteristics?: string[];
}

const deviceList = ref<BleDevice[]>([]);
const connected = ref(false);
const scanning = ref(false);

// 特征值订阅相关状态
const subscribeCharacteristics = ref<string[]>(['00002a29-0000-1000-8000-00805f9b34fb']);
const notifications = ref<CharacteristicNotification[]>([]);
const showSubscriptionDialog = ref(false);

// 监听扫描更新
let scanListener: (() => void) | null = null;

// 监听连接更新
let connectionListener: (() => void) | null = null;

// 监听特征值通知
let notificationListener: (() => void) | null = null;

onMounted(async () => {
  try {
    // 初始化蓝牙适配器
    await invoke("get_adapter");

    // 启动连接状态监听器
    await invoke("start_connection_listener");

    // 启动特征值通知监听器
    await invoke("start_notification_listener");

    // 监听连接状态更新事件
    connectionListener = await listen<boolean>("connection-update", (event) => {
      connected.value = event.payload;
    });

    // 监听特征值通知事件
    notificationListener = await listen<CharacteristicNotification>("characteristic-notification", (event) => {
      const notification = { ...event.payload, id: Date.now().toString() };
      notifications.value.unshift(notification);

      // 限制通知列表长度
      if (notifications.value.length > 100) {
        notifications.value = notifications.value.slice(0, 100);
      }

      console.log('收到特征值通知:', {
        设备: notification.device_address,
        特征值: notification.characteristic_uuid,
        数据: notification.data.map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
        时间: new Date(notification.timestamp * 1000).toLocaleString()
      });
    });
  } catch (error) {
    console.error("Error initializing Bluetooth:", error);
  }
});

const chooseDevice = ref<BleDevice>();

async function handleUpdateSwitch(value: boolean | null) {
  scanning.value = value as boolean;

  if (value) {
    deviceList.value = [];
    try {
      // 先开始扫描（传入厂商 ID 0x08e2）
      await invoke("start_scan", { args: { manufacturer_id: 0x08e2 } });

      // 再启动扫描监听器（此时广播通道已就绪）
      await invoke("start_scan_listener");
      console.log("start_scan param:", 0x08e2);

      // 监听扫描结果事件（后端已过滤，直接赋值）
      scanListener = await listen<BleDevice[]>("scan-update", (event) => {
        deviceList.value = event.payload;
      });
    } catch (error) {
      console.error("Error starting scan:", error);
      scanning.value = false;
    }
  } else {
    try {
      await invoke("stop_scan");
      // 停止扫描监听器
      if (scanListener) {
        scanListener();
        scanListener = null;
      }
    } catch (error) {
      console.error("Error stopping scan:", error);
    }
  }
}

// 添加特征值输入框
function addCharacteristic() {
  subscribeCharacteristics.value.push('');
}

// 删除特征值输入框
function removeCharacteristic(index: number) {
  subscribeCharacteristics.value.splice(index, 1);
}

// 基本连接（不订阅特征值）
async function handleBasicConnect() {
  if (!chooseDevice.value) return;

  try {
    if (chooseDevice.value?.is_connected) {
      await invoke("disconnect_device", {
        address: chooseDevice.value.address,
      });
      chooseDevice.value.is_connected = false;
    } else {
      await invoke("connect_device", {
        address: chooseDevice.value.address,
      });
      chooseDevice.value.is_connected = true;

      // 获取设备服务
      const services = await invoke<string[]>("get_device_services", {
        address: chooseDevice.value.address,
      });
      chooseDevice.value.services = services;
    }
  } catch (error) {
    console.error("Device connection error:", error);
  }
}

// 连接并订阅特征值
async function handleConnectWithSubscription() {
  if (!chooseDevice.value) return;

  const validUuids = subscribeCharacteristics.value.filter(uuid => uuid.trim() !== '');
  if (validUuids.length === 0) {
    alert('请至少添加一个特征值 UUID');
    return;
  }

  try {
    const args: ConnectDeviceArgs = {
      address: chooseDevice.value.address,
      subscribeCharacteristics: validUuids
    };

    await invoke("connect_device_with_subscription", { args });
    chooseDevice.value.is_connected = true;

    // 获取设备服务
    const services = await invoke<string[]>("get_device_services", {
      address: chooseDevice.value.address,
    });
    chooseDevice.value.services = services;

    showSubscriptionDialog.value = false;
    console.log('设备连接成功并已订阅特征值:', validUuids);
  } catch (error) {
    console.error("Connection with subscription error:", error);
    alert(`连接失败: ${error}`);
  }
}

// 兼容旧的连接函数
async function handleDeviceConnect() {
  await handleBasicConnect();
}

// 格式化通知数据
function formatNotificationData(data: number[]): string {
  return data.map(byte => `0x${byte.toString(16).padStart(2, '0')}`).join(' ');
}

// 格式化时间戳
function formatTimestamp(timestamp: number): string {
  return new Date(timestamp * 1000).toLocaleTimeString();
}

onUnmounted(() => {
  // 停止扫描
  if (scanning.value) {
    invoke("stop_scan");
  }

  // 清理事件监听器
  if (scanListener) {
    scanListener();
  }
  if (connectionListener) {
    connectionListener();
  }
  if (notificationListener) {
    notificationListener();
  }
});
</script>

<style scoped></style>
