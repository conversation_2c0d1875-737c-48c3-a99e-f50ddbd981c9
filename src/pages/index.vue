<template>
  <div class="flex flex-col h-screen bg-white rounded-2xl shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h1 class="flex items-center gap-2 text-lg font-semibold">
        <span class="icon-[mdi--bluetooth-audio] text-2xl text-blue-500" />
        <span class="text-blue-500">蓝牙管理</span>
      </h1>
      <div class="flex items-center gap-3">
        <v-btn circle icon="mdi-cog-outline" size="large" />
        <v-btn circle icon="mdi-refresh" size="large" />

        <v-switch
          color="primary"
          :label="scanning ? '停止搜索' : '开始搜索'"
          :model-value="scanning"
          @update:model-value="handleUpdateSwitch"
        />
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b text-red-500">
      <h2 class="font-semibold text-red-500">已连接设备</h2>
    </div>

    <!-- Body -->
    <div class="flex flex-1 divide-x">
      <!-- Nearby Devices -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">附近设备</h2>
        <div class="space-y-3 h-[calc(100dvh-200px)] overflow-y-auto">
          <template v-for="(bleDevice, key) in deviceList" :key="key">
            <div
              class="p-3 border rounded-xl hover:bg-gray-50"
              @click="chooseDevice = bleDevice"
            >
              <div class="flex justify-between items-center">
                <p class="font-medium">{{ bleDevice.name }}</p>
                <p v-if="bleDevice.is_connected" class="text-green-600 text-sm">
                  •已连接
                </p>
              </div>
              <div class="flex gap-6">
                <p class="w-1/2 overflow-hidden">
                  <span class="30">&nbsp;&nbsp;{{ bleDevice.address }}</span>
                </p>
                <p class="w-1/2 text-sm">信号强度: {{ bleDevice.rssi }}</p>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Device Details -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">设备详情</h2>
        <div v-if="chooseDevice" class="p-4 border rounded-xl">
          <p>
            <span class="font-medium">设备名称:</span> {{ chooseDevice?.name }}
          </p>
          <p>
            <span class="font-medium">状态:</span>
            {{ chooseDevice?.is_connected ? "已连接" : "未连接" }}
          </p>
          <p><span class="font-medium">电量:</span> 80%</p>
          <p><span class="font-medium">服务:</span></p>
          <p v-for="(service, key) in chooseDevice?.services" :key="key">
            {{ service }}
          </p>
          <div class="flex gap-2 mt-4">
            <button
              class="px-3 py-1 border rounded-lg hover:bg-gray-100"
              @click="handleDeviceConnect(chooseDevice)"
            >
              {{ chooseDevice?.is_connected ? "断开连接" : "连接设备" }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <!-- <div class="flex justify-between items-center p-3 border-t text-sm text-gray-600">
      <span>Adapter: Intel Wireless Bluetooth v5.3</span>
      <span>Connected: 2 devices</span>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { listen } from "@tauri-apps/api/event";
import { invoke } from "@tauri-apps/api/core";
import { onMounted, onUnmounted, ref } from "vue";

// 定义设备类型
interface BleDevice {
  name: string;
  address: string;
  rssi: number;
  is_connected: boolean;
  manufacturer_data: Record<string, number[]>;
  services: string[];
}

const deviceList = ref<BleDevice[]>([]);
const connected = ref(false);
const scanning = ref(false);

// 监听扫描更新
let scanListener: (() => void) | null = null;

// 监听连接更新
let connectionListener: (() => void) | null = null;

onMounted(async () => {
  try {
    // 初始化蓝牙适配器
    await invoke("get_adapter");

    // 启动连接状态监听器
    await invoke("start_connection_listener");

    // 监听连接状态更新事件
    connectionListener = await listen<boolean>("connection-update", (event) => {
      connected.value = event.payload;
    });
  } catch (error) {
    console.error("Error initializing Bluetooth:", error);
  }
});

const chooseDevice = ref<BleDevice>();

async function handleUpdateSwitch(value: boolean | null) {
  scanning.value = value as boolean;

  if (value) {
    deviceList.value = [];
    try {
      // 先开始扫描（传入厂商 ID 0x08e2）
      await invoke("start_scan", { args: { manufacturer_id: 0x08e2 } });

      // 再启动扫描监听器（此时广播通道已就绪）
      await invoke("start_scan_listener");
      console.log("start_scan param:", 0x08e2);

      // 监听扫描结果事件（后端已过滤，直接赋值）
      scanListener = await listen<BleDevice[]>("scan-update", (event) => {
        deviceList.value = event.payload;
      });
    } catch (error) {
      console.error("Error starting scan:", error);
      scanning.value = false;
    }
  } else {
    try {
      await invoke("stop_scan");
      // 停止扫描监听器
      if (scanListener) {
        scanListener();
        scanListener = null;
      }
    } catch (error) {
      console.error("Error stopping scan:", error);
    }
  }
}

async function handleDeviceConnect(device: BleDevice) {
  if (!chooseDevice.value) {
    return;
  }

  try {
    if (chooseDevice.value?.is_connected) {
      await invoke("disconnect_device", {
        address: chooseDevice.value.address,
      });
      // 更新本地设备状态
      chooseDevice.value.is_connected = false;
    } else {
      await invoke("connect_device", {
        address: chooseDevice.value.address,
      });
      // 更新本地设备状态
      chooseDevice.value.is_connected = true;

      // 获取设备服务
      const services = await invoke<string[]>("get_device_services", {
        address: chooseDevice.value.address,
      });
      chooseDevice.value.services = services;

      
    }
  } catch (error) {
    console.error("Device connection error:", error);
  }
}

onUnmounted(() => {
  // 停止扫描
  if (scanning.value) {
    invoke("stop_scan");
  }

  // 清理事件监听器
  if (scanListener) {
    scanListener();
  }
  if (connectionListener) {
    connectionListener();
  }
});
</script>

<style scoped></style>
