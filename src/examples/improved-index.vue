<template>
  <div class="flex flex-col h-screen bg-white rounded-2xl shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h1 class="flex items-center gap-2 text-lg font-semibold">
        <span class="icon-[mdi--bluetooth-audio] text-2xl text-blue-500" />
        <span class="text-blue-500">蓝牙管理</span>
      </h1>
      <div class="flex items-center gap-3">
        <div v-if="error" class="text-red-500 text-sm">{{ error }}</div>
        <div class="flex items-center gap-2">
          <span class="text-sm">{{ isInitialized ? '已初始化' : '未初始化' }}</span>
          <div :class="isInitialized ? 'bg-green-500' : 'bg-red-500'" class="w-2 h-2 rounded-full"></div>
        </div>
        <v-switch
          color="primary"
          :label="isScanning ? '停止搜索' : '开始搜索'"
          :model-value="isScanning"
          @update:model-value="handleScanToggle"
          :disabled="!isInitialized"
        />
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b text-red-500">
      <h2 class="font-semibold text-red-500">
        已连接设备 {{ isConnected ? '(已连接)' : '(未连接)' }}
      </h2>
      <div class="text-sm">
        通知: {{ notifications.length }}
        <button @click="clearNotifications" class="ml-2 text-blue-500 hover:underline">清除</button>
      </div>
    </div>

    <!-- Body -->
    <div class="flex flex-1 divide-x">
      <!-- Nearby Devices -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">附近设备 ({{ devices.length }})</h2>
        <div class="space-y-3 h-[calc(100dvh-200px)] overflow-y-auto">
          <template v-for="device in devices" :key="device.address">
            <div
              class="p-3 border rounded-xl hover:bg-gray-50 cursor-pointer"
              :class="{ 'bg-green-50 border-green-200': device.is_connected }"
              @click="selectedDevice = device"
            >
              <div class="flex justify-between items-center">
                <p class="font-medium">{{ device.name }}</p>
                <p v-if="device.is_connected" class="text-green-600 text-sm">
                  •已连接
                </p>
              </div>
              <div class="flex gap-6">
                <p class="w-1/2 overflow-hidden text-sm text-gray-600">
                  {{ device.address }}
                </p>
                <p class="w-1/2 text-sm text-gray-600">信号: {{ device.rssi }}dBm</p>
              </div>
              <div v-if="device.services.length > 0" class="mt-2">
                <p class="text-xs text-gray-500">服务: {{ device.services.length }} 个</p>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Device Details -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">设备详情</h2>
        <div v-if="selectedDevice" class="space-y-4">
          <!-- 设备信息 -->
          <div class="p-4 border rounded-xl">
            <h3 class="font-medium mb-2">基本信息</h3>
            <div class="space-y-1 text-sm">
              <p><span class="font-medium">名称:</span> {{ selectedDevice.name }}</p>
              <p><span class="font-medium">地址:</span> {{ selectedDevice.address }}</p>
              <p><span class="font-medium">状态:</span> 
                <span :class="selectedDevice.is_connected ? 'text-green-600' : 'text-gray-600'">
                  {{ selectedDevice.is_connected ? "已连接" : "未连接" }}
                </span>
              </p>
              <p><span class="font-medium">信号强度:</span> {{ selectedDevice.rssi }}dBm</p>
            </div>
          </div>

          <!-- 服务列表 -->
          <div v-if="selectedDevice.services.length > 0" class="p-4 border rounded-xl">
            <h3 class="font-medium mb-2">设备服务 ({{ selectedDevice.services.length }})</h3>
            <div class="space-y-1 max-h-32 overflow-y-auto">
              <div v-for="service in selectedDevice.services" :key="service" class="text-xs font-mono bg-gray-100 p-1 rounded">
                {{ service }}
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-2">
            <button
              v-if="!selectedDevice.is_connected"
              @click="handleBasicConnect"
              class="px-4 py-2 border rounded-lg hover:bg-gray-100"
            >
              基本连接
            </button>
            <button
              v-if="!selectedDevice.is_connected"
              @click="showSubscriptionDialog = true"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              订阅连接
            </button>
            <button
              v-if="selectedDevice.is_connected"
              @click="handleDisconnect"
              class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              断开连接
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 特征值订阅对话框 -->
    <div v-if="showSubscriptionDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto">
        <h3 class="text-lg font-semibold mb-4">连接并订阅特征值</h3>
        
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">设备: {{ selectedDevice?.name }}</p>
          <p class="text-sm text-gray-600 mb-4">地址: {{ selectedDevice?.address }}</p>
        </div>

        <div class="mb-4">
          <h4 class="font-medium mb-2">要订阅的特征值 UUID:</h4>
          <div v-for="(uuid, index) in subscribeUuids" :key="index" class="flex gap-2 mb-2">
            <input 
              v-model="subscribeUuids[index]" 
              placeholder="例如: 6e400003-b534-f393-67a9-e50e24dcca9e"
              class="flex-1 px-3 py-2 border rounded-lg text-sm"
            />
            <button 
              @click="removeUuid(index)" 
              class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 text-sm"
            >
              删除
            </button>
          </div>
          <button 
            @click="addUuid" 
            class="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 text-sm"
          >
            添加特征值
          </button>
        </div>

        <div class="mb-4">
          <h4 class="font-medium mb-2">常用特征值:</h4>
          <div class="space-y-1 text-sm">
            <button 
              v-for="(uuid, name) in CHARACTERISTIC_UUIDS" 
              :key="name"
              @click="subscribeUuids[0] = uuid"
              class="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
            >
              {{ name.replace(/_/g, ' ') }}
            </button>
          </div>
        </div>

        <div class="flex gap-2">
          <button 
            @click="handleSubscriptionConnect"
            class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            连接并订阅
          </button>
          <button 
            @click="showSubscriptionDialog = false"
            class="px-4 py-2 border rounded-lg hover:bg-gray-100"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 通知显示区域 -->
    <div v-if="notifications.length > 0" class="fixed bottom-4 right-4 w-80 max-h-60 bg-white border rounded-lg shadow-lg overflow-hidden">
      <div class="p-3 bg-gray-50 border-b flex justify-between items-center">
        <h4 class="font-medium">实时通知 ({{ notifications.length }})</h4>
        <button 
          @click="clearNotifications"
          class="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      <div class="overflow-y-auto max-h-48">
        <div 
          v-for="notification in notifications.slice(0, 10)" 
          :key="`${notification.device_address}-${notification.timestamp}`"
          class="p-2 border-b text-xs"
        >
          <div class="flex justify-between items-center mb-1">
            <span class="font-medium text-blue-600">{{ notification.device_address.slice(-8) }}</span>
            <span class="text-gray-500">{{ formatTimestamp(notification.timestamp) }}</span>
          </div>
          <div class="text-green-600 mb-1">{{ notification.characteristic_uuid.slice(0, 8) }}...</div>
          <div class="font-mono bg-gray-100 p-1 rounded">{{ formatData(notification.data) }}</div>
          
          <!-- 解析特定类型的数据 -->
          <div v-if="notification.characteristic_uuid === CHARACTERISTIC_UUIDS.HEART_RATE_MEASUREMENT" class="mt-1 text-red-600">
            心率: {{ parseHeartRateData(notification.data)?.heartRate || 'N/A' }} BPM
          </div>
          <div v-else-if="notification.characteristic_uuid === CHARACTERISTIC_UUIDS.NORDIC_UART_TX" class="mt-1 text-purple-600">
            消息: {{ parseNordicUartData(notification.data) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useBLE } from '@/composables/useBLE';

// 使用 BLE 组合式函数
const {
  devices,
  notifications,
  isScanning,
  isConnected,
  isInitialized,
  error,
  startScan,
  stopScan,
  connectDevice,
  connectDeviceWithSubscription,
  disconnectDevice,
  clearNotifications,
  formatData,
  formatTimestamp,
  parseHeartRateData,
  parseNordicUartData,
  CHARACTERISTIC_UUIDS,
} = useBLE();

// 本地状态
const selectedDevice = ref();
const showSubscriptionDialog = ref(false);
const subscribeUuids = ref(['']);

// 扫描切换
const handleScanToggle = async (value: boolean) => {
  try {
    if (value) {
      await startScan(0x08e2); // 可选的厂商 ID 过滤
    } else {
      await stopScan();
    }
  } catch (error) {
    console.error('扫描切换失败:', error);
  }
};

// 基本连接
const handleBasicConnect = async () => {
  if (!selectedDevice.value) return;
  
  try {
    await connectDevice(selectedDevice.value.address);
  } catch (error) {
    console.error('基本连接失败:', error);
  }
};

// 断开连接
const handleDisconnect = async () => {
  if (!selectedDevice.value) return;
  
  try {
    await disconnectDevice(selectedDevice.value.address);
  } catch (error) {
    console.error('断开连接失败:', error);
  }
};

// 订阅连接
const handleSubscriptionConnect = async () => {
  if (!selectedDevice.value) return;

  const validUuids = subscribeUuids.value.filter(uuid => uuid.trim() !== '');
  if (validUuids.length === 0) {
    alert('请至少添加一个特征值 UUID');
    return;
  }

  try {
    await connectDeviceWithSubscription(selectedDevice.value.address, validUuids);
    showSubscriptionDialog.value = false;
  } catch (error) {
    console.error('订阅连接失败:', error);
  }
};

// UUID 管理
const addUuid = () => {
  subscribeUuids.value.push('');
};

const removeUuid = (index: number) => {
  subscribeUuids.value.splice(index, 1);
};
</script>

<style scoped>
/* 自定义样式 */
</style>
