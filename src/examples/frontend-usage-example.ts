/**
 * 前端调用特征值订阅功能的示例代码
 * 
 * 这个文件展示了如何在前端使用新的特征值订阅功能
 */

import { invoke } from '@tauri-apps/api/core';
import { listen, type UnlistenFn } from '@tauri-apps/api/event';

// 类型定义
interface BleDevice {
  name: string;
  address: string;
  rssi: number;
  is_connected: boolean;
  manufacturer_data: Record<string, number[]>;
  services: string[];
}

interface CharacteristicNotification {
  device_address: string;
  characteristic_uuid: string;
  data: number[];
  timestamp: number;
}

interface ConnectDeviceArgs {
  address: string;
  subscribeCharacteristics?: string[];
}

// 常用特征值 UUID
export const COMMON_CHARACTERISTIC_UUIDS = {
  // Nordic UART Service
  NORDIC_UART_RX: '6e400002-b534-f393-67a9-e50e24dcca9e',
  NORDIC_UART_TX: '6e400003-b534-f393-67a9-e50e24dcca9e',
  
  // 标准 BLE 服务
  HEART_RATE_MEASUREMENT: '00002a37-0000-1000-8000-00805f9b34fb',
  BATTERY_LEVEL: '00002a19-0000-1000-8000-00805f9b34fb',
  DEVICE_NAME: '00002a00-0000-1000-8000-00805f9b34fb',
  MANUFACTURER_NAME: '00002a29-0000-1000-8000-00805f9b34fb',
} as const;

/**
 * BLE 管理类
 */
export class BLEManager {
  private scanListener: UnlistenFn | null = null;
  private connectionListener: UnlistenFn | null = null;
  private notificationListener: UnlistenFn | null = null;
  
  private devices: BleDevice[] = [];
  private notifications: CharacteristicNotification[] = [];
  
  // 事件回调
  public onDevicesUpdate?: (devices: BleDevice[]) => void;
  public onConnectionUpdate?: (connected: boolean) => void;
  public onNotification?: (notification: CharacteristicNotification) => void;

  /**
   * 初始化 BLE 管理器
   */
  async initialize(): Promise<void> {
    try {
      // 获取蓝牙适配器
      await invoke('get_adapter');
      
      // 启动监听器
      await invoke('start_scan_listener');
      await invoke('start_connection_listener');
      await invoke('start_notification_listener');
      
      // 设置事件监听
      await this.setupEventListeners();
      
      console.log('BLE 管理器初始化成功');
    } catch (error) {
      console.error('BLE 管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private async setupEventListeners(): Promise<void> {
    // 监听扫描更新
    this.scanListener = await listen<BleDevice[]>('scan-update', (event) => {
      this.devices = event.payload;
      this.onDevicesUpdate?.(this.devices);
    });

    // 监听连接状态
    this.connectionListener = await listen<boolean>('connection-update', (event) => {
      this.onConnectionUpdate?.(event.payload);
    });

    // 监听特征值通知
    this.notificationListener = await listen<CharacteristicNotification>('characteristic-notification', (event) => {
      const notification = event.payload;
      this.notifications.unshift(notification);
      
      // 限制通知历史长度
      if (this.notifications.length > 1000) {
        this.notifications = this.notifications.slice(0, 1000);
      }
      
      this.onNotification?.(notification);
      
      console.log('收到特征值通知:', {
        设备: notification.device_address,
        特征值: notification.characteristic_uuid,
        数据: this.formatData(notification.data),
        时间: new Date(notification.timestamp * 1000).toLocaleString()
      });
    });
  }

  /**
   * 开始扫描设备
   */
  async startScan(manufacturerId?: number): Promise<void> {
    try {
      const args = manufacturerId ? { manufacturer_id: manufacturerId } : undefined;
      await invoke('start_scan', { args });
      console.log('开始扫描设备...');
    } catch (error) {
      console.error('开始扫描失败:', error);
      throw error;
    }
  }

  /**
   * 停止扫描设备
   */
  async stopScan(): Promise<void> {
    try {
      await invoke('stop_scan');
      console.log('停止扫描设备');
    } catch (error) {
      console.error('停止扫描失败:', error);
      throw error;
    }
  }

  /**
   * 基本连接设备（不订阅特征值）
   */
  async connectDevice(address: string): Promise<void> {
    try {
      await invoke('connect_device', { address });
      console.log(`设备 ${address} 连接成功`);
    } catch (error) {
      console.error(`设备 ${address} 连接失败:`, error);
      throw error;
    }
  }

  /**
   * 连接设备并订阅特征值
   */
  async connectDeviceWithSubscription(
    address: string, 
    characteristicUuids: string[]
  ): Promise<void> {
    try {
      const args: ConnectDeviceArgs = {
        address,
        subscribeCharacteristics: characteristicUuids
      };
      
      await invoke('connect_device_with_subscription', { args });
      console.log(`设备 ${address} 连接成功并已订阅特征值:`, characteristicUuids);
    } catch (error) {
      console.error(`设备 ${address} 连接订阅失败:`, error);
      throw error;
    }
  }

  /**
   * 断开设备连接
   */
  async disconnectDevice(address: string): Promise<void> {
    try {
      await invoke('disconnect_device', { address });
      console.log(`设备 ${address} 已断开连接`);
    } catch (error) {
      console.error(`设备 ${address} 断开连接失败:`, error);
      throw error;
    }
  }

  /**
   * 获取设备服务
   */
  async getDeviceServices(address: string): Promise<string[]> {
    try {
      const services = await invoke<string[]>('get_device_services', { address });
      console.log(`设备 ${address} 的服务:`, services);
      return services;
    } catch (error) {
      console.error(`获取设备 ${address} 服务失败:`, error);
      throw error;
    }
  }

  /**
   * 格式化数据为十六进制字符串
   */
  formatData(data: number[]): string {
    return data.map(byte => `0x${byte.toString(16).padStart(2, '0')}`).join(' ');
  }

  /**
   * 解析心率数据
   */
  parseHeartRateData(data: number[]): { heartRate: number; sensorContact: boolean } | null {
    if (data.length < 2) return null;
    
    const flags = data[0];
    const is16Bit = (flags & 0x01) !== 0;
    
    let heartRate: number;
    if (is16Bit) {
      heartRate = (data[2] << 8) | data[1];
    } else {
      heartRate = data[1];
    }
    
    return {
      heartRate,
      sensorContact: (flags & 0x06) === 0x06
    };
  }

  /**
   * 解析 Nordic UART 数据
   */
  parseNordicUartData(data: number[]): string {
    try {
      return new TextDecoder('utf-8').decode(new Uint8Array(data));
    } catch (error) {
      console.error('Nordic UART 数据解析失败:', error);
      return this.formatData(data);
    }
  }

  /**
   * 获取当前设备列表
   */
  getDevices(): BleDevice[] {
    return [...this.devices];
  }

  /**
   * 获取通知历史
   */
  getNotifications(): CharacteristicNotification[] {
    return [...this.notifications];
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 停止扫描
    try {
      await this.stopScan();
    } catch (error) {
      console.error('停止扫描时出错:', error);
    }

    // 清理事件监听器
    if (this.scanListener) {
      this.scanListener();
      this.scanListener = null;
    }
    if (this.connectionListener) {
      this.connectionListener();
      this.connectionListener = null;
    }
    if (this.notificationListener) {
      this.notificationListener();
      this.notificationListener = null;
    }

    console.log('BLE 管理器已清理');
  }
}

/**
 * 使用示例
 */
export async function exampleUsage() {
  const bleManager = new BLEManager();

  // 设置事件回调
  bleManager.onDevicesUpdate = (devices) => {
    console.log('设备列表更新:', devices);
  };

  bleManager.onConnectionUpdate = (connected) => {
    console.log('连接状态更新:', connected);
  };

  bleManager.onNotification = (notification) => {
    console.log('收到通知:', notification);
    
    // 根据特征值类型解析数据
    if (notification.characteristic_uuid === COMMON_CHARACTERISTIC_UUIDS.HEART_RATE_MEASUREMENT) {
      const heartRateData = bleManager.parseHeartRateData(notification.data);
      if (heartRateData) {
        console.log('心率数据:', heartRateData);
      }
    } else if (notification.characteristic_uuid === COMMON_CHARACTERISTIC_UUIDS.NORDIC_UART_TX) {
      const message = bleManager.parseNordicUartData(notification.data);
      console.log('Nordic UART 消息:', message);
    }
  };

  try {
    // 初始化
    await bleManager.initialize();

    // 开始扫描
    await bleManager.startScan();

    // 等待发现设备...
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 连接设备并订阅特征值
    const deviceAddress = 'AA:BB:CC:DD:EE:FF'; // 替换为实际设备地址
    await bleManager.connectDeviceWithSubscription(deviceAddress, [
      COMMON_CHARACTERISTIC_UUIDS.NORDIC_UART_TX,
      COMMON_CHARACTERISTIC_UUIDS.HEART_RATE_MEASUREMENT
    ]);

    // 监听通知...
    console.log('正在监听通知，按 Ctrl+C 退出');

  } catch (error) {
    console.error('示例执行失败:', error);
  } finally {
    // 清理资源
    await bleManager.cleanup();
  }
}

// 导出默认实例
export const defaultBLEManager = new BLEManager();
