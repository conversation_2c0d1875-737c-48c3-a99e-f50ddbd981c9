/**
 * Vue 3 组合式函数：useBLE
 * 
 * 提供 BLE 设备管理和特征值订阅功能
 */

import { ref, onMounted, onUnmounted, type Ref } from 'vue';
import { invoke } from '@tauri-apps/api/core';
import { listen, type UnlistenFn } from '@tauri-apps/api/event';

// 类型定义
export interface BleDevice {
  name: string;
  address: string;
  rssi: number;
  is_connected: boolean;
  manufacturer_data: Record<string, number[]>;
  services: string[];
}

export interface CharacteristicNotification {
  device_address: string;
  characteristic_uuid: string;
  data: number[];
  timestamp: number;
}

export interface ConnectDeviceArgs {
  address: string;
  subscribeCharacteristics?: string[];
}

// 常用特征值 UUID
export const CHARACTERISTIC_UUIDS = {
  NORDIC_UART_RX: '6e400002-b534-f393-67a9-e50e24dcca9e',
  NORDIC_UART_TX: '6e400003-b534-f393-67a9-e50e24dcca9e',
  HEART_RATE_MEASUREMENT: '00002a37-0000-1000-8000-00805f9b34fb',
  BATTERY_LEVEL: '00002a19-0000-1000-8000-00805f9b34fb',
  DEVICE_NAME: '00002a00-0000-1000-8000-00805f9b34fb',
  MANUFACTURER_NAME: '00002a29-0000-1000-8000-00805f9b34fb',
} as const;

export interface UseBLEOptions {
  autoInitialize?: boolean;
  maxNotifications?: number;
}

/**
 * BLE 管理组合式函数
 */
export function useBLE(options: UseBLEOptions = {}) {
  const { autoInitialize = true, maxNotifications = 100 } = options;

  // 响应式状态
  const devices: Ref<BleDevice[]> = ref([]);
  const notifications: Ref<CharacteristicNotification[]> = ref([]);
  const isScanning = ref(false);
  const isConnected = ref(false);
  const isInitialized = ref(false);
  const error = ref<string | null>(null);

  // 事件监听器
  let scanListener: UnlistenFn | null = null;
  let connectionListener: UnlistenFn | null = null;
  let notificationListener: UnlistenFn | null = null;

  /**
   * 初始化 BLE
   */
  const initialize = async (): Promise<void> => {
    try {
      error.value = null;
      
      // 获取蓝牙适配器
      await invoke('get_adapter');
      
      // 启动监听器
      await invoke('start_scan_listener');
      await invoke('start_connection_listener');
      await invoke('start_notification_listener');
      
      // 设置事件监听
      await setupEventListeners();
      
      isInitialized.value = true;
      console.log('BLE 初始化成功');
    } catch (err) {
      error.value = `BLE 初始化失败: ${err}`;
      console.error('BLE 初始化失败:', err);
      throw err;
    }
  };

  /**
   * 设置事件监听器
   */
  const setupEventListeners = async (): Promise<void> => {
    // 监听扫描更新
    scanListener = await listen<BleDevice[]>('scan-update', (event) => {
      devices.value = event.payload;
    });

    // 监听连接状态
    connectionListener = await listen<boolean>('connection-update', (event) => {
      isConnected.value = event.payload;
    });

    // 监听特征值通知
    notificationListener = await listen<CharacteristicNotification>('characteristic-notification', (event) => {
      const notification = event.payload;
      notifications.value.unshift(notification);
      
      // 限制通知历史长度
      if (notifications.value.length > maxNotifications) {
        notifications.value = notifications.value.slice(0, maxNotifications);
      }
    });
  };

  /**
   * 开始扫描
   */
  const startScan = async (manufacturerId?: number): Promise<void> => {
    try {
      error.value = null;
      const args = manufacturerId ? { manufacturer_id: manufacturerId } : undefined;
      await invoke('start_scan', { args });
      isScanning.value = true;
    } catch (err) {
      error.value = `开始扫描失败: ${err}`;
      throw err;
    }
  };

  /**
   * 停止扫描
   */
  const stopScan = async (): Promise<void> => {
    try {
      error.value = null;
      await invoke('stop_scan');
      isScanning.value = false;
    } catch (err) {
      error.value = `停止扫描失败: ${err}`;
      throw err;
    }
  };

  /**
   * 连接设备
   */
  const connectDevice = async (address: string): Promise<void> => {
    try {
      error.value = null;
      await invoke('connect_device', { address });
    } catch (err) {
      error.value = `连接设备失败: ${err}`;
      throw err;
    }
  };

  /**
   * 连接设备并订阅特征值
   */
  const connectDeviceWithSubscription = async (
    address: string,
    characteristicUuids: string[]
  ): Promise<void> => {
    try {
      error.value = null;
      const args: ConnectDeviceArgs = {
        address,
        subscribeCharacteristics: characteristicUuids
      };
      await invoke('connect_device_with_subscription', { args });
    } catch (err) {
      error.value = `连接订阅失败: ${err}`;
      throw err;
    }
  };

  /**
   * 断开连接
   */
  const disconnectDevice = async (address: string): Promise<void> => {
    try {
      error.value = null;
      await invoke('disconnect_device', { address });
    } catch (err) {
      error.value = `断开连接失败: ${err}`;
      throw err;
    }
  };

  /**
   * 获取设备服务
   */
  const getDeviceServices = async (address: string): Promise<string[]> => {
    try {
      error.value = null;
      return await invoke<string[]>('get_device_services', { address });
    } catch (err) {
      error.value = `获取设备服务失败: ${err}`;
      throw err;
    }
  };

  /**
   * 清除通知历史
   */
  const clearNotifications = (): void => {
    notifications.value = [];
  };

  /**
   * 格式化数据
   */
  const formatData = (data: number[]): string => {
    return data.map(byte => `0x${byte.toString(16).padStart(2, '0')}`).join(' ');
  };

  /**
   * 格式化时间戳
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleTimeString();
  };

  /**
   * 解析心率数据
   */
  const parseHeartRateData = (data: number[]): { heartRate: number; sensorContact: boolean } | null => {
    if (data.length < 2) return null;
    
    const flags = data[0];
    const is16Bit = (flags & 0x01) !== 0;
    
    let heartRate: number;
    if (is16Bit) {
      heartRate = (data[2] << 8) | data[1];
    } else {
      heartRate = data[1];
    }
    
    return {
      heartRate,
      sensorContact: (flags & 0x06) === 0x06
    };
  };

  /**
   * 解析 Nordic UART 数据
   */
  const parseNordicUartData = (data: number[]): string => {
    try {
      return new TextDecoder('utf-8').decode(new Uint8Array(data));
    } catch (error) {
      console.error('Nordic UART 数据解析失败:', error);
      return formatData(data);
    }
  };

  /**
   * 清理资源
   */
  const cleanup = async (): Promise<void> => {
    // 停止扫描
    if (isScanning.value) {
      try {
        await stopScan();
      } catch (error) {
        console.error('停止扫描时出错:', error);
      }
    }

    // 清理事件监听器
    if (scanListener) {
      scanListener();
      scanListener = null;
    }
    if (connectionListener) {
      connectionListener();
      connectionListener = null;
    }
    if (notificationListener) {
      notificationListener();
      notificationListener = null;
    }

    isInitialized.value = false;
  };

  // 生命周期钩子
  onMounted(async () => {
    if (autoInitialize) {
      try {
        await initialize();
      } catch (error) {
        console.error('自动初始化失败:', error);
      }
    }
  });

  onUnmounted(() => {
    cleanup();
  });

  // 返回响应式状态和方法
  return {
    // 响应式状态
    devices,
    notifications,
    isScanning,
    isConnected,
    isInitialized,
    error,

    // 方法
    initialize,
    startScan,
    stopScan,
    connectDevice,
    connectDeviceWithSubscription,
    disconnectDevice,
    getDeviceServices,
    clearNotifications,
    cleanup,

    // 工具函数
    formatData,
    formatTimestamp,
    parseHeartRateData,
    parseNordicUartData,

    // 常量
    CHARACTERISTIC_UUIDS,
  };
}
